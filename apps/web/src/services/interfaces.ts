/**
 * Service Layer Interfaces
 * 
 * Defines contracts for all service classes to ensure consistency
 * and enable proper dependency injection.
 */

import type { PrismaClient } from '../../prisma/generated';

// Base service interface
export interface IBaseService {
  readonly prisma: PrismaClient;
  readonly logger: ILogger;
}

// Logger interface
export interface ILogger {
  info(message: string, ...args: any[]): void;
  error(message: string, error?: Error, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  debug(message: string, ...args: any[]): void;
}

// Service operation result
export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Pagination options
export interface PaginationOptions {
  limit?: number;
  offset?: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

// Account service interface
export interface IAccountService extends IBaseService {
  getMonitoredAccounts(userId: string, options?: PaginationOptions): Promise<ServiceResult>;
  addAccount(userId: string, handle: string, syncSettings?: AccountSyncSettings): Promise<ServiceResult>;
  removeAccount(userId: string, accountId: string): Promise<ServiceResult>;
  toggleAccountStatus(userId: string, accountId: string, isActive: boolean): Promise<ServiceResult>;
  updateSyncSettings(userId: string, accountId: string, settings: AccountSyncSettings): Promise<ServiceResult>;
  discoverSmartAccounts(userId: string, options: SmartAccountDiscoveryOptions): Promise<ServiceResult>;
  getSectorRecommendations(userId: string, sector: string, limit?: number): Promise<ServiceResult>;
  validateAccountOwnership(userId: string, accountId: string): Promise<boolean>;
}

// User service interface
export interface IUserService extends IBaseService {
  getProfile(userId: string): Promise<ServiceResult>;
  updateProfile(userId: string, updates: UserProfileUpdate): Promise<ServiceResult>;
  getUsage(userId: string): Promise<ServiceResult>;
  canUseFeature(userId: string, feature: string): Promise<ServiceResult>;
  logUsage(userId: string, feature: string, amount: number, metadata?: any): Promise<ServiceResult>;
  getPersonality(userId: string): Promise<ServiceResult>;
  updatePersonality(userId: string, updates: PersonalityUpdate): Promise<ServiceResult>;
  getAvailableModels(): Promise<ServiceResult>;
  getSelectedModel(userId: string): Promise<ServiceResult>;
  updateSelectedModel(userId: string, modelId?: string): Promise<ServiceResult>;
  getAvailablePersonalities(): Promise<ServiceResult>;
}

// Crypto service interface
export interface ICryptoService extends IBaseService {
  testConnection(userId: string): Promise<ServiceResult>;
  getSectors(userId: string): Promise<ServiceResult>;
  getSmartFollowers(userId: string, options: SmartFollowersOptions): Promise<ServiceResult>;
  getAccountFeed(userId: string, options: AccountFeedOptions): Promise<ServiceResult>;
  searchProjects(userId: string, options: ProjectSearchOptions): Promise<ServiceResult>;
  getTrendingProjects(userId: string, options: TrendingProjectsOptions): Promise<ServiceResult>;
  getProjectMetrics(userId: string, options: ProjectMetricsOptions): Promise<ServiceResult>;
  getCompetitiveAnalysis(userId: string, projectSlug: string): Promise<ServiceResult>;
  findSectorInfluencers(userId: string, options: SectorInfluencersOptions): Promise<ServiceResult>;
  getMarketIntelligence(userId: string, options: MarketIntelligenceOptions): Promise<ServiceResult>;
  clearCache(options: CacheClearOptions): Promise<ServiceResult>;
  getCacheStats(): Promise<ServiceResult>;
  cleanupCache(): Promise<ServiceResult>;
}

// Subscription service interface
export interface ISubscriptionService extends IBaseService {
  checkFeatureAccess(userId: string, feature: string): Promise<ServiceResult>;
  recordUsage(userId: string, feature: string, amount: number, metadata?: any): Promise<ServiceResult>;
  getUsageStats(userId: string, features?: string[]): Promise<ServiceResult>;
  getRateLimits(userId: string): Promise<ServiceResult>;
  upgradeSubscription(userId: string, planId: string): Promise<ServiceResult>;
  cancelSubscription(userId: string): Promise<ServiceResult>;
}

// Type definitions for options and updates
export interface AccountSyncSettings {
  syncMentions: boolean;
  syncUserTweets: boolean;
  syncReplies: boolean;
  syncRetweets: boolean;
}

export interface SmartAccountDiscoveryOptions {
  targetSector?: string;
  seedAccount?: string;
  limit?: number;
}

export interface UserProfileUpdate {
  name?: string;
  avatar?: string;
  metadata?: Record<string, any>;
}

export interface PersonalityUpdate {
  personalityId?: string | null;
  customSystemPrompt?: string;
  useFirstPerson?: boolean;
}

export interface SmartFollowersOptions {
  username?: string;
  userId?: string;
  limit?: number;
}

export interface AccountFeedOptions {
  username?: string;
  userId?: string;
  startDate?: string;
  endDate?: string;
  type?: 'Original' | 'Reply' | 'Quote';
  hasMedia?: boolean;
  sortBy?: 'CreatedAt' | 'Impressions';
  sortOrder?: 'Ascending' | 'Descending';
  limit?: number;
}

export interface ProjectSearchOptions {
  searchQuery?: string;
  projectSlug?: string;
  sectorSlug?: string;
  type?: 'Original' | 'Reply' | 'Quote';
  startDate?: string;
  endDate?: string;
  sortBy?: 'SmartEngagementPoints' | 'Impressions' | 'MatchingTweetsCount' | 'Mindshare';
  sortOrder?: 'Ascending' | 'Descending';
  mindshareTimeframe?: '_7Days' | '_30Days';
  granulation?: '_1Hour' | '_24Hours';
  metricType?: 'Impressions' | 'EngagementRate' | 'Mentions';
  limit?: number;
}

export interface TrendingProjectsOptions {
  sectorSlug?: string;
  timeframe?: '_7Days' | '_30Days';
  limit?: number;
}

export interface ProjectMetricsOptions {
  projectSlug: string;
  metricType: 'Impressions' | 'EngagementRate' | 'Mentions';
  granulation: '_1Hour' | '_24Hours';
  startDate?: string;
  endDate?: string;
}

export interface SectorInfluencersOptions {
  username: string;
  targetSector: string;
  limit?: number;
}

export interface MarketIntelligenceOptions {
  keywords?: string[];
  sectorSlug?: string;
  projectSlugs?: string[];
  timeframe?: '_7Days' | '_30Days';
}

export interface CacheClearOptions {
  type?: 'sectors' | 'trending' | 'all';
  sectorSlug?: string;
  timeframe?: string;
}

// Service factory interface
export interface IServiceFactory {
  createAccountService(): IAccountService;
  createUserService(): IUserService;
  createCryptoService(): ICryptoService;
  createMentionService(): IMentionService;
  createTwitterService(): ITwitterService;
  createBenjiService(): IBenjiService;
  createSubscriptionService(): ISubscriptionService;
}

// Additional service interfaces
export interface IMentionService extends IBaseService {
  getMentions(userId: string, options?: any): Promise<ServiceResult>;
  getMentionById(userId: string, mentionId: string): Promise<ServiceResult>;
  createMentionFromUrl(userId: string, url: string): Promise<ServiceResult>;
  updateBullishScore(userId: string, mentionId: string, score: number): Promise<ServiceResult>;
  updateImportanceScore(userId: string, mentionId: string, score: number): Promise<ServiceResult>;
  archiveMention(userId: string, mentionId: string): Promise<ServiceResult>;
  unarchiveMention(userId: string, mentionId: string): Promise<ServiceResult>;
  bulkArchiveMentions(userId: string, mentionIds: string[]): Promise<ServiceResult>;
  deleteMention(userId: string, mentionId: string): Promise<ServiceResult>;
  markMentionAsReplied(userId: string, mentionId: string): Promise<ServiceResult>;
  calculateBullishScore(content: string): number;
  extractKeywords(content: string): string[];
}

export interface ITwitterService extends IBaseService {
  getUserInfo(handle: string): Promise<ServiceResult>;
  validateHandle(handle: string): boolean;
  validateTwitterUrl(url: string): boolean;
  extractTweetId(url: string): string | null;
  normalizeTwitterUrl(url: string): string;
  getTweetFromUrl(url: string): Promise<ServiceResult>;
  checkTwitterApiHealth(): Promise<ServiceResult>;
}

export interface IBenjiService extends IBaseService {
  generateMentionResponse(userId: string, options: any): Promise<ServiceResult>;
  generateQuickReply(userId: string, options: any): Promise<ServiceResult>;
  analyzeContent(userId: string, content: string, authorInfo?: any): Promise<ServiceResult>;
  getConversationContext(userId: string, conversationId?: string): Promise<ServiceResult>;
  clearConversationContext(userId: string, conversationId?: string): Promise<ServiceResult>;
  getUserAISettings(userId: string): Promise<ServiceResult>;
  updateUserAISettings(userId: string, settings: any): Promise<ServiceResult>;
  getAvailableModels(userId: string): Promise<ServiceResult>;
}