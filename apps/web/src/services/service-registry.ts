/**
 * Service Registry
 * 
 * Provides centralized service management with dependency injection,
 * singleton patterns, and service lifecycle management.
 */

import type { PrismaClient } from '../../prisma/generated';
import type { 
  IServiceFactory, 
  IAccountService, 
  IUserService, 
  ICryptoService, 
  IMentionService,
  ITwitterService,
  IBenjiService,
  ISubscriptionService,
  ILogger 
} from './interfaces';
import { createLogger } from './logger.service';
import { AccountService } from './account.service';
import { UserService } from './user.service';
import { CryptoService } from './crypto.service';
import { MentionService } from './mention.service';
import { TwitterService } from './twitter.service';
import { BenjiService } from './benji.service';
import { SubscriptionService } from './subscription.service';

/**
 * Service Registry - Manages all service instances and their dependencies
 */
export class ServiceRegistry {
  private readonly prisma: PrismaClient;
  private readonly logger: ILogger;
  private readonly services: Map<string, any> = new Map();
  private readonly factories: Map<string, () => any> = new Map();
  private initialized = false;

  constructor(prisma: PrismaClient, logger?: ILogger) {
    this.prisma = prisma;
    this.logger = logger || createLogger('ServiceRegistry');
    this.setupFactories();
  }

  /**
   * Setup service factories for lazy initialization
   */
  private setupFactories(): void {
    this.factories.set('account', () => new AccountService(
      this.prisma,
      createLogger('AccountService')
    ));

    this.factories.set('user', () => new UserService(
      this.prisma,
      createLogger('UserService')
    ));

    this.factories.set('crypto', () => new CryptoService(
      this.prisma,
      createLogger('CryptoService')
    ));

    this.factories.set('mention', () => new MentionService(
      this.prisma,
      createLogger('MentionService')
    ));

    this.factories.set('twitter', () => new TwitterService(
      this.prisma,
      createLogger('TwitterService')
    ));

    this.factories.set('benji', () => new BenjiService(
      this.prisma,
      createLogger('BenjiService')
    ));

    this.factories.set('subscription', () => new SubscriptionService(
      this.prisma,
      createLogger('SubscriptionService')
    ));
  }

  /**
   * Get or create a service instance
   */
  private getService<T>(serviceName: string): T {
    if (!this.services.has(serviceName)) {
      const factory = this.factories.get(serviceName);
      if (!factory) {
        throw new Error(`Service '${serviceName}' not found in registry`);
      }
      
      const service = factory();
      this.services.set(serviceName, service);
      this.logger.debug(`Created service instance: ${serviceName}`);
    }
    
    return this.services.get(serviceName);
  }

  /**
   * Get account service instance
   */
  getAccountService(): IAccountService {
    return this.getService<IAccountService>('account');
  }

  /**
   * Get user service instance
   */
  getUserService(): IUserService {
    return this.getService<IUserService>('user');
  }

  /**
   * Get crypto service instance
   */
  getCryptoService(): ICryptoService {
    return this.getService<ICryptoService>('crypto');
  }

  /**
   * Get mention service instance
   */
  getMentionService(): IMentionService {
    return this.getService<IMentionService>('mention');
  }

  /**
   * Get Twitter service instance
   */
  getTwitterService(): ITwitterService {
    return this.getService<ITwitterService>('twitter');
  }

  /**
   * Get Benji service instance
   */
  getBenjiService(): IBenjiService {
    return this.getService<IBenjiService>('benji');
  }

  /**
   * Get subscription service instance
   */
  getSubscriptionService(): ISubscriptionService {
    return this.getService<ISubscriptionService>('subscription');
  }

  /**
   * Initialize all services (for warming up)
   */
  async initializeServices(): Promise<void> {
    if (this.initialized) {
      return;
    }

    this.logger.info('Initializing all services...');
    
    const serviceNames = Array.from(this.factories.keys());
    
    for (const serviceName of serviceNames) {
      try {
        this.getService(serviceName);
        this.logger.debug(`Initialized service: ${serviceName}`);
      } catch (error) {
        this.logger.error(`Failed to initialize service '${serviceName}':`, error);
        throw error;
      }
    }

    this.initialized = true;
    this.logger.info(`Successfully initialized ${serviceNames.length} services`);
  }

  /**
   * Reset all service instances (useful for testing)
   */
  resetServices(): void {
    this.services.clear();
    this.initialized = false;
    this.logger.info('All services reset');
  }

  /**
   * Get service health check
   */
  async getServiceHealth(): Promise<{
    healthy: boolean;
    services: Record<string, { initialized: boolean; error?: string }>;
  }> {
    const serviceNames = Array.from(this.factories.keys());
    const serviceHealth: Record<string, { initialized: boolean; error?: string }> = {};
    let allHealthy = true;

    for (const serviceName of serviceNames) {
      try {
        const service = this.getService(serviceName);
        serviceHealth[serviceName] = { initialized: true };
      } catch (error) {
        serviceHealth[serviceName] = { 
          initialized: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
        allHealthy = false;
      }
    }

    return {
      healthy: allHealthy,
      services: serviceHealth,
    };
  }

  /**
   * Get service metrics
   */
  getServiceMetrics(): {
    totalServices: number;
    initializedServices: number;
    availableServices: string[];
    initializedServiceNames: string[];
  } {
    const totalServices = this.factories.size;
    const initializedServices = this.services.size;
    const availableServices = Array.from(this.factories.keys());
    const initializedServiceNames = Array.from(this.services.keys());

    return {
      totalServices,
      initializedServices,
      availableServices,
      initializedServiceNames,
    };
  }

  /**
   * Register a custom service
   */
  registerService<T>(name: string, factory: () => T): void {
    if (this.factories.has(name)) {
      throw new Error(`Service '${name}' is already registered`);
    }
    
    this.factories.set(name, factory);
    this.logger.debug(`Registered custom service: ${name}`);
  }

  /**
   * Unregister a service
   */
  unregisterService(name: string): void {
    this.factories.delete(name);
    this.services.delete(name);
    this.logger.debug(`Unregistered service: ${name}`);
  }

  /**
   * Check if a service is registered
   */
  hasService(name: string): boolean {
    return this.factories.has(name);
  }

  /**
   * Check if a service is initialized
   */
  isServiceInitialized(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get all available service names
   */
  getAvailableServices(): string[] {
    return Array.from(this.factories.keys());
  }

  /**
   * Dispose of all services and clean up resources
   */
  dispose(): void {
    this.logger.info('Disposing service registry...');
    this.services.clear();
    this.factories.clear();
    this.initialized = false;
    this.logger.info('Service registry disposed');
  }
}

// Default registry instance
let defaultRegistry: ServiceRegistry | null = null;

/**
 * Get the default service registry instance
 */
export function getServiceRegistry(prisma?: PrismaClient): ServiceRegistry {
  if (!defaultRegistry) {
    if (!prisma) {
      throw new Error('PrismaClient is required to initialize ServiceRegistry');
    }
    defaultRegistry = new ServiceRegistry(prisma);
  }
  return defaultRegistry;
}

/**
 * Reset the default service registry (useful for testing)
 */
export function resetServiceRegistry(): void {
  if (defaultRegistry) {
    defaultRegistry.dispose();
    defaultRegistry = null;
  }
}

/**
 * Initialize the default service registry
 */
export async function initializeServiceRegistry(prisma?: PrismaClient): Promise<ServiceRegistry> {
  const registry = getServiceRegistry(prisma);
  await registry.initializeServices();
  return registry;
}
