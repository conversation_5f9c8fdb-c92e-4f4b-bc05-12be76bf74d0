/**
 * Benji Service
 * 
 * Handles all AI-related operations including:
 * - Response generation for mentions
 * - Content analysis and sentiment scoring
 * - AI-powered enhancements
 * - Conversation context management
 */

import { BaseService } from "./base.service";
import type { ServiceResult } from "./interfaces";
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { FeatureType } from "../../prisma/generated";

export interface AIAnalysisResult {
  bullishScore: number;
  importanceScore: number;
  keywords: string[];
  analysisData: {
    priority: "high" | "medium" | "low";
    sentiment: "positive" | "negative" | "neutral";
    confidence: number;
    topics: string[];
    recommendations: string[];
  };
}

export interface AIResponseOptions {
  mentionId: string;
  mentionContent: string;
  monitoredAccountInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  mentionAuthorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  personality?: string;
  customPrompt?: string;
  enhancedMode?: boolean;
}

export interface AIResponseResult {
  content: string;
  model: string;
  tokensUsed: number;
  confidence?: number;
  processingTime: number;
  quality: "high" | "medium" | "low";
}

export interface QuickReplyOptions {
  tweetContent: string;
  tweetAuthor?: {
    name: string;
    handle: string;
  };
  responseType?: "professional" | "casual" | "supportive" | "engaging";
  maxLength?: number;
}

export interface ConversationContext {
  conversationId: string;
  messages: Array<{
    role: "user" | "assistant";
    content: string;
    timestamp: Date;
  }>;
  metadata: {
    totalTokens: number;
    lastActivity: Date;
    topics: string[];
  };
}

export class BenjiService extends BaseService {
  private benjiInstanceCache = new Map<string, any>();

  /**
   * Get or create Benji instance for user
   */
  private async getBenjiInstance(userId: string): Promise<any> {
    if (this.benjiInstanceCache.has(userId)) {
      return this.benjiInstanceCache.get(userId);
    }

    try {
      this.logger.info("Creating new Benji instance for user", { userId });
      
      const { getBenjiForUser } = await import("../lib/benji");
      const benji = await getBenjiForUser(userId);
      
      this.benjiInstanceCache.set(userId, benji);
      return benji;
    } catch (error) {
      this.logger.error("Error creating Benji instance", error, { userId });
      throw error;
    }
  }

  /**
   * Generate AI response for a mention
   */
  async generateMentionResponse(
    userId: string,
    options: AIResponseOptions
  ): Promise<ServiceResult<AIResponseResult>> {
    try {
      this.logger.info("Generating AI response for mention", { userId, mentionId: options.mentionId });

      // Check rate limits
      const rateLimit = await checkRateLimit(userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        return {
          success: false,
          error: `AI calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        };
      }

      // Get Benji instance
      const benji = await this.getBenjiInstance(userId);
      
      const startTime = Date.now();
      
      // Generate response based on mode
      let result;
      if (options.enhancedMode) {
        result = await benji.generateEnhancedMentionResponse(
          options.mentionContent,
          options
        );
      } else {
        result = await benji.generateMentionResponse(
          options.mentionContent,
          options
        );
      }

      // Process streaming result
      let responseText = "";
      let chunkCount = 0;
      
      for await (const chunk of result.textStream) {
        chunkCount++;
        responseText += chunk;
        
        // Log progress for long responses
        if (chunkCount % 10 === 0) {
          this.logger.debug("Processing AI response chunks", { 
            userId, 
            mentionId: options.mentionId, 
            chunkCount, 
            currentLength: responseText.length 
          });
        }
      }

      if (!responseText.trim()) {
        return {
          success: false,
          error: "AI generated empty response. Please try again.",
        };
      }

      const processingTime = Date.now() - startTime;
      const usage = await result.usage;
      const tokensUsed = usage?.totalTokens || 0;

      // Determine quality based on length and processing time
      const quality = this.determineResponseQuality(responseText, processingTime, tokensUsed);

      // Record usage
      await recordUsage(userId, FeatureType.AI_CALLS, 1, {
        mentionId: options.mentionId,
        model: options.enhancedMode ? "enhanced-benji" : "benji",
        tokensUsed,
        processingTime,
        quality,
      });

      return {
        success: true,
        data: {
          content: responseText,
          model: options.enhancedMode ? "enhanced-benji-o3" : "benji-gemini",
          tokensUsed,
          processingTime,
          quality,
        },
      };
    } catch (error) {
      this.logger.error("Error generating AI response", error, { userId, mentionId: options.mentionId });
      return {
        success: false,
        error: "Failed to generate AI response",
      };
    }
  }

  /**
   * Generate quick reply for any tweet
   */
  async generateQuickReply(
    userId: string,
    options: QuickReplyOptions
  ): Promise<ServiceResult<AIResponseResult>> {
    try {
      this.logger.info("Generating quick reply", { userId, tweetLength: options.tweetContent.length });

      // Check rate limits
      const rateLimit = await checkRateLimit(userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        return {
          success: false,
          error: `AI calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        };
      }

      // Get Benji instance
      const benji = await this.getBenjiInstance(userId);
      
      const startTime = Date.now();
      
      // Generate quick reply
      const result = await benji.generateQuickReply(
        options.tweetContent,
        {
          responseType: options.responseType || "engaging",
          maxLength: options.maxLength || 280,
          tweetAuthor: options.tweetAuthor,
        }
      );

      // Process streaming result
      let responseText = "";
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      if (!responseText.trim()) {
        return {
          success: false,
          error: "AI generated empty response. Please try again.",
        };
      }

      const processingTime = Date.now() - startTime;
      const usage = await result.usage;
      const tokensUsed = usage?.totalTokens || 0;

      // Determine quality
      const quality = this.determineResponseQuality(responseText, processingTime, tokensUsed);

      // Record usage
      await recordUsage(userId, FeatureType.AI_CALLS, 1, {
        operation: "quick-reply",
        responseType: options.responseType,
        tokensUsed,
        processingTime,
        quality,
      });

      return {
        success: true,
        data: {
          content: responseText,
          model: "benji-quick-reply",
          tokensUsed,
          processingTime,
          quality,
        },
      };
    } catch (error) {
      this.logger.error("Error generating quick reply", error, { userId });
      return {
        success: false,
        error: "Failed to generate quick reply",
      };
    }
  }

  /**
   * Analyze content with AI
   */
  async analyzeContent(
    userId: string,
    content: string,
    authorInfo?: {
      name: string;
      handle: string;
      followers?: number;
      verified?: boolean;
      avatarUrl?: string;
    }
  ): Promise<ServiceResult<AIAnalysisResult>> {
    try {
      this.logger.info("Analyzing content with AI", { userId, contentLength: content.length });

      // Check rate limits
      const rateLimit = await checkRateLimit(userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        return {
          success: false,
          error: `AI calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        };
      }

      // Get Benji instance
      const benji = await this.getBenjiInstance(userId);
      
      // Perform analysis
      const analysis = await benji.performFullAnalysis(content, authorInfo);

      // Record usage
      await recordUsage(userId, FeatureType.AI_CALLS, 1, {
        operation: "content-analysis",
        contentLength: content.length,
        bullishScore: analysis.bullishScore,
        importanceScore: analysis.importanceScore,
      });

      return {
        success: true,
        data: {
          bullishScore: analysis.bullishScore,
          importanceScore: analysis.importanceScore,
          keywords: analysis.keywords,
          analysisData: analysis.analysisData,
        },
      };
    } catch (error) {
      this.logger.error("Error analyzing content", error, { userId, contentLength: content.length });
      return {
        success: false,
        error: "Failed to analyze content",
      };
    }
  }

  /**
   * Get conversation context for user
   */
  async getConversationContext(
    userId: string,
    conversationId?: string
  ): Promise<ServiceResult<ConversationContext>> {
    try {
      this.logger.info("Getting conversation context", { userId, conversationId });

      // Get Benji instance
      const benji = await this.getBenjiInstance(userId);
      
      // Get conversation context
      const context = await benji.getConversationContext(conversationId);

      return {
        success: true,
        data: context,
      };
    } catch (error) {
      this.logger.error("Error getting conversation context", error, { userId, conversationId });
      return {
        success: false,
        error: "Failed to get conversation context",
      };
    }
  }

  /**
   * Clear conversation context
   */
  async clearConversationContext(
    userId: string,
    conversationId?: string
  ): Promise<ServiceResult<{ cleared: boolean }>> {
    try {
      this.logger.info("Clearing conversation context", { userId, conversationId });

      // Get Benji instance
      const benji = await this.getBenjiInstance(userId);
      
      // Clear context
      await benji.clearConversationContext(conversationId);

      return {
        success: true,
        data: { cleared: true },
        message: "Conversation context cleared",
      };
    } catch (error) {
      this.logger.error("Error clearing conversation context", error, { userId, conversationId });
      return {
        success: false,
        error: "Failed to clear conversation context",
      };
    }
  }

  /**
   * Get user's AI model preferences
   */
  async getUserAISettings(userId: string): Promise<ServiceResult<{
    selectedModel: string;
    personality: string | null;
    customPrompt: string | null;
    enhancedMode: boolean;
  }>> {
    try {
      this.logger.info("Getting user AI settings", { userId });

      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return {
          success: false,
          error: "User not found",
        };
      }

      // Determine available models based on plan
      const planName = user.clerkPlanName || "Free";

      let selectedModel = "gemini-2.5-flash"; // Default
      let enhancedMode = false;

      if (planName === "Pro") {
        selectedModel = "gemini-2.5-pro";
        enhancedMode = true;
      } else if (planName === "Enterprise") {
        selectedModel = "openai-o1-mini";
        enhancedMode = true;
      }

      return {
        success: true,
        data: {
          selectedModel,
          personality: null, // TODO: Implement personality system
          customPrompt: null, // TODO: Implement custom prompts
          enhancedMode,
        },
      };
    } catch (error) {
      this.logger.error("Error getting user AI settings", error, { userId });
      return {
        success: false,
        error: "Failed to get AI settings",
      };
    }
  }

  /**
   * Update user's AI settings
   */
  async updateUserAISettings(
    userId: string,
    settings: {
      personalityId?: string | null;
      customPrompt?: string;
      enhancedMode?: boolean;
    }
  ): Promise<ServiceResult<{ updated: boolean }>> {
    try {
      this.logger.info("Updating user AI settings", { userId, settings });

      // Clear cached Benji instance since settings changed
      this.benjiInstanceCache.delete(userId);

      // Update user settings
      const updates: any = {};
      if (settings.personalityId !== undefined) {
        updates.personalityId = settings.personalityId;
      }

      if (Object.keys(updates).length > 0) {
        await this.prisma.user.update({
          where: { id: userId },
          data: updates,
        });
      }

      // Update personality if custom prompt provided
      if (settings.customPrompt !== undefined) {
        if (settings.personalityId) {
          await this.prisma.personalityProfile.update({
            where: { id: settings.personalityId },
            data: {
              systemPrompt: settings.customPrompt,
            },
          });
        }
      }

      return {
        success: true,
        data: { updated: true },
        message: "AI settings updated successfully",
      };
    } catch (error) {
      this.logger.error("Error updating user AI settings", error, { userId, settings });
      return {
        success: false,
        error: "Failed to update AI settings",
      };
    }
  }

  /**
   * Get available AI models for user's plan
   */
  async getAvailableModels(userId: string): Promise<ServiceResult<{
    models: Array<{
      id: string;
      name: string;
      description: string;
      available: boolean;
      planRequired: string;
    }>;
    currentModel: string;
  }>> {
    try {
      this.logger.info("Getting available AI models", { userId });

      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      const planName = user?.clerkPlanName || "Free";

      const models = [
        {
          id: "gemini-2.5-flash",
          name: "Gemini 2.5 Flash",
          description: "Fast and efficient responses",
          available: true,
          planRequired: "Free",
        },
        {
          id: "gemini-2.5-pro",
          name: "Gemini 2.5 Pro",
          description: "Advanced reasoning with tool access",
          available: ["Pro", "Enterprise"].includes(planName),
          planRequired: "Pro",
        },
        {
          id: "openai-o1-mini",
          name: "OpenAI o1-mini",
          description: "Cutting-edge AI with all tools",
          available: planName === "Enterprise",
          planRequired: "Enterprise",
        },
      ];

      // Determine current model
      let currentModel = "gemini-2.5-flash";
      if (planName === "Pro") {
        currentModel = "gemini-2.5-pro";
      } else if (planName === "Enterprise") {
        currentModel = "openai-o1-mini";
      }

      return {
        success: true,
        data: {
          models,
          currentModel,
        },
      };
    } catch (error) {
      this.logger.error("Error getting available models", error, { userId });
      return {
        success: false,
        error: "Failed to get available models",
      };
    }
  }

  /**
   * Determine response quality based on metrics
   */
  private determineResponseQuality(
    content: string,
    processingTime: number,
    tokensUsed: number
  ): "high" | "medium" | "low" {
    const contentLength = content.length;
    
    // Quality scoring based on multiple factors
    let score = 0;
    
    // Length scoring (optimal range 100-500 characters)
    if (contentLength >= 100 && contentLength <= 500) {
      score += 3;
    } else if (contentLength >= 50 && contentLength <= 800) {
      score += 2;
    } else {
      score += 1;
    }
    
    // Processing time scoring (prefer reasonable response times)
    if (processingTime < 3000) { // Under 3 seconds
      score += 2;
    } else if (processingTime < 10000) { // Under 10 seconds
      score += 1;
    }
    
    // Token efficiency scoring
    if (tokensUsed > 0) {
      const tokensPerChar = tokensUsed / contentLength;
      if (tokensPerChar < 0.5) { // Efficient token usage
        score += 2;
      } else if (tokensPerChar < 1.0) {
        score += 1;
      }
    }
    
    // Determine quality tier
    if (score >= 6) {
      return "high";
    } else if (score >= 4) {
      return "medium";
    } else {
      return "low";
    }
  }
}