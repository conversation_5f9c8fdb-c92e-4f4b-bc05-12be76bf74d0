/**
 * Performance Monitor Service
 * 
 * Provides comprehensive performance monitoring for service operations
 * including timing, caching, and resource usage tracking.
 */

import type { PrismaClient } from '../../prisma/generated';
import { BaseService } from './base.service';
import type { ILogger, ServiceResult } from './interfaces';

interface PerformanceMetric {
  operation: string;
  duration: number;
  startTime: Date;
  endTime: Date;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

interface CacheMetric {
  key: string;
  hit: boolean;
  duration: number;
  size?: number;
  ttl?: number;
}

interface ResourceMetric {
  resource: string;
  usage: number;
  limit: number;
  timestamp: Date;
}

export class PerformanceMonitorService extends BaseService {
  private readonly metrics: Map<string, PerformanceMetric[]> = new Map();
  private readonly cacheMetrics: Map<string, CacheMetric[]> = new Map();
  private readonly resourceMetrics: Map<string, ResourceMetric[]> = new Map();
  private readonly maxMetricsPerOperation = 1000; // Prevent memory leaks
  private readonly metricsRetentionMs = 24 * 60 * 60 * 1000; // 24 hours

  constructor(prisma: PrismaClient, logger: ILogger) {
    super(prisma, logger);
    
    // Cleanup old metrics every hour
    setInterval(() => this.cleanupOldMetrics(), 60 * 60 * 1000);
  }

  /**
   * Start timing an operation
   */
  startTimer(operationName: string): () => void {
    const startTime = new Date();
    const startHrTime = process.hrtime();
    
    return () => {
      const endTime = new Date();
      const [seconds, nanoseconds] = process.hrtime(startHrTime);
      const duration = seconds * 1000 + nanoseconds / 1000000; // Convert to milliseconds
      
      this.recordMetric(operationName, {
        operation: operationName,
        duration,
        startTime,
        endTime,
        success: true,
      });
    };
  }

  /**
   * Record a performance metric
   */
  recordMetric(operation: string, metric: PerformanceMetric): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    const operationMetrics = this.metrics.get(operation)!;
    operationMetrics.push(metric);
    
    // Keep only the most recent metrics
    if (operationMetrics.length > this.maxMetricsPerOperation) {
      operationMetrics.shift();
    }
  }

  /**
   * Record a cache metric
   */
  recordCacheMetric(key: string, hit: boolean, duration: number, size?: number, ttl?: number): void {
    if (!this.cacheMetrics.has(key)) {
      this.cacheMetrics.set(key, []);
    }
    
    const metrics = this.cacheMetrics.get(key)!;
    metrics.push({ key, hit, duration, size, ttl });
    
    // Keep only recent cache metrics
    if (metrics.length > this.maxMetricsPerOperation) {
      metrics.shift();
    }
  }

  /**
   * Record a resource usage metric
   */
  recordResourceUsage(resource: string, usage: number, limit: number): void {
    if (!this.resourceMetrics.has(resource)) {
      this.resourceMetrics.set(resource, []);
    }
    
    const metrics = this.resourceMetrics.get(resource)!;
    metrics.push({ resource, usage, limit, timestamp: new Date() });
    
    // Keep only recent resource metrics
    if (metrics.length > this.maxMetricsPerOperation) {
      metrics.shift();
    }
  }

  /**
   * Get performance statistics for an operation
   */
  getOperationStats(operation: string): ServiceResult {
    try {
      const metrics = this.metrics.get(operation);
      if (!metrics || metrics.length === 0) {
        return this.success({
          operation,
          count: 0,
          averageDuration: 0,
          minDuration: 0,
          maxDuration: 0,
          successRate: 0,
          errors: [],
        });
      }

      const durations = metrics.map(m => m.duration);
      const successCount = metrics.filter(m => m.success).length;
      const errors = metrics.filter(m => !m.success).map(m => m.error).filter(Boolean);

      const stats = {
        operation,
        count: metrics.length,
        averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
        minDuration: Math.min(...durations),
        maxDuration: Math.max(...durations),
        successRate: (successCount / metrics.length) * 100,
        errors: [...new Set(errors)], // Unique errors
        recentMetrics: metrics.slice(-10), // Last 10 metrics
      };

      return this.success(stats);
    } catch (error) {
      return this.handleError(error, 'getOperationStats');
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): ServiceResult {
    try {
      const allCacheMetrics = Array.from(this.cacheMetrics.values()).flat();
      const totalRequests = allCacheMetrics.length;
      const hitCount = allCacheMetrics.filter(m => m.hit).length;
      const missCount = totalRequests - hitCount;
      const hitRate = totalRequests > 0 ? (hitCount / totalRequests) * 100 : 0;

      const stats = {
        totalRequests,
        hitCount,
        missCount,
        hitRate,
        averageResponseTime: totalRequests > 0 
          ? allCacheMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests 
          : 0,
        keyCount: this.cacheMetrics.size,
      };

      return this.success(stats);
    } catch (error) {
      return this.handleError(error, 'getCacheStats');
    }
  }

  /**
   * Get resource usage statistics
   */
  getResourceStats(): ServiceResult {
    try {
      const resourceStats = new Map<string, any>();
      
      for (const [resource, metrics] of this.resourceMetrics) {
        if (metrics.length === 0) continue;
        
        const latest = metrics[metrics.length - 1];
        const usageHistory = metrics.slice(-24); // Last 24 data points
        
        resourceStats.set(resource, {
          resource,
          currentUsage: latest.usage,
          limit: latest.limit,
          utilizationRate: (latest.usage / latest.limit) * 100,
          averageUsage: usageHistory.reduce((sum, m) => sum + m.usage, 0) / usageHistory.length,
          peakUsage: Math.max(...usageHistory.map(m => m.usage)),
          lastUpdated: latest.timestamp,
        });
      }

      return this.success({
        resources: Object.fromEntries(resourceStats),
        totalResources: resourceStats.size,
        highUtilization: Array.from(resourceStats.values()).filter(r => r.utilizationRate > 80),
      });
    } catch (error) {
      return this.handleError(error, 'getResourceStats');
    }
  }

  /**
   * Get comprehensive system health metrics
   */
  getSystemHealth(): ServiceResult {
    try {
      const now = Date.now();
      const oneMinuteAgo = now - 60000;
      const fiveMinutesAgo = now - 300000;
      
      // Get recent metrics
      const recentMetrics = Array.from(this.metrics.values())
        .flat()
        .filter(m => m.endTime.getTime() > oneMinuteAgo);
      
      const recentErrors = recentMetrics.filter(m => !m.success);
      const averageLatency = recentMetrics.length > 0 
        ? recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length
        : 0;
      
      // Calculate error rate
      const errorRate = recentMetrics.length > 0 
        ? (recentErrors.length / recentMetrics.length) * 100 
        : 0;
      
      // Get resource utilization
      const resourceStats = this.getResourceStats();
      const highUtilizationResources = resourceStats.success 
        ? resourceStats.data.highUtilization.length 
        : 0;
      
      // Determine overall health
      const isHealthy = errorRate < 5 && averageLatency < 1000 && highUtilizationResources === 0;
      
      const healthData = {
        healthy: isHealthy,
        timestamp: new Date(),
        metrics: {
          requestsPerMinute: recentMetrics.length,
          averageLatency,
          errorRate,
          successRate: 100 - errorRate,
        },
        resources: {
          totalResources: this.resourceMetrics.size,
          highUtilization: highUtilizationResources,
        },
        cache: this.getCacheStats().data,
        alerts: [
          ...(errorRate > 10 ? ['High error rate detected'] : []),
          ...(averageLatency > 2000 ? ['High latency detected'] : []),
          ...(highUtilizationResources > 0 ? ['High resource utilization detected'] : []),
        ],
      };

      return this.success(healthData);
    } catch (error) {
      return this.handleError(error, 'getSystemHealth');
    }
  }

  /**
   * Get slowest operations
   */
  getSlowestOperations(limit: number = 10): ServiceResult {
    try {
      const allMetrics = Array.from(this.metrics.entries())
        .map(([operation, metrics]) => {
          const avgDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
          const maxDuration = Math.max(...metrics.map(m => m.duration));
          const count = metrics.length;
          
          return {
            operation,
            averageDuration: avgDuration,
            maxDuration,
            count,
            recentSample: metrics.slice(-5),
          };
        })
        .sort((a, b) => b.averageDuration - a.averageDuration)
        .slice(0, limit);

      return this.success({
        slowestOperations: allMetrics,
        totalOperations: this.metrics.size,
      });
    } catch (error) {
      return this.handleError(error, 'getSlowestOperations');
    }
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): ServiceResult {
    try {
      this.metrics.clear();
      this.cacheMetrics.clear();
      this.resourceMetrics.clear();
      
      this.logger.info('All performance metrics cleared');
      return this.success({ message: 'All metrics cleared successfully' });
    } catch (error) {
      return this.handleError(error, 'clearMetrics');
    }
  }

  /**
   * Cleanup old metrics to prevent memory leaks
   */
  private cleanupOldMetrics(): void {
    const cutoffTime = Date.now() - this.metricsRetentionMs;
    let totalCleaned = 0;
    
    // Clean performance metrics
    for (const [operation, metrics] of this.metrics) {
      const filteredMetrics = metrics.filter(m => m.endTime.getTime() > cutoffTime);
      if (filteredMetrics.length !== metrics.length) {
        this.metrics.set(operation, filteredMetrics);
        totalCleaned += metrics.length - filteredMetrics.length;
      }
    }
    
    // Clean resource metrics
    for (const [resource, metrics] of this.resourceMetrics) {
      const filteredMetrics = metrics.filter(m => m.timestamp.getTime() > cutoffTime);
      if (filteredMetrics.length !== metrics.length) {
        this.resourceMetrics.set(resource, filteredMetrics);
        totalCleaned += metrics.length - filteredMetrics.length;
      }
    }
    
    if (totalCleaned > 0) {
      this.logger.info(`Cleaned up ${totalCleaned} old performance metrics`);
    }
  }

  /**
   * Create a performance-monitored wrapper for service methods
   */
  monitorServiceMethod<T extends (...args: any[]) => Promise<ServiceResult>>(
    serviceName: string,
    methodName: string,
    method: T
  ): T {
    const operationName = `${serviceName}.${methodName}`;
    
    return (async (...args: any[]) => {
      const timer = this.startTimer(operationName);
      const startTime = new Date();
      
      try {
        const result = await method(...args);
        const endTime = new Date();
        const duration = endTime.getTime() - startTime.getTime();
        
        this.recordMetric(operationName, {
          operation: operationName,
          duration,
          startTime,
          endTime,
          success: result.success,
          error: result.success ? undefined : result.error,
          metadata: {
            args: args.length,
            hasData: !!result.data,
          },
        });
        
        timer();
        return result;
      } catch (error) {
        const endTime = new Date();
        const duration = endTime.getTime() - startTime.getTime();
        
        this.recordMetric(operationName, {
          operation: operationName,
          duration,
          startTime,
          endTime,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          metadata: {
            args: args.length,
            errorType: error instanceof Error ? error.constructor.name : 'Unknown',
          },
        });
        
        timer();
        throw error;
      }
    }) as T;
  }
}
