// Unified Component Library Index
// This file exports all unified components for easy importing

// Core UI Components
export { Button, buttonVariants, type ButtonProps } from "./button";
export { default as IconButton } from "../atoms/icon-button";
export { default as PrimaryButton } from "../atoms/button";

// Modal & Dialog Components
export { Modal, ConfirmationModal, useModal, type ModalProps } from "./modal";
// Note: Use ConfirmationModal directly instead of ConfirmationDialog
export { ConfirmationModal as ConfirmationDialog } from "./modal";

// Form Components
export {
  Form,
  FormField,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  useForm,
  validateField,
  type FormProps,
  type FormFieldProps,
  type FormLabelProps,
  type FormControlProps,
  type FormDescriptionProps,
  type FormMessageProps,
} from "./form";

// Input Components
export { Input } from "./input";
export { Textarea } from "./textarea";
export { Checkbox } from "./checkbox";
export { Label } from "./label";

// Loading Components
export {
  Loading,
  PageLoading,
  InlineLoading,
  ButtonLoading,
  LoadingOverlay,
  SkeletonText,
  SkeletonCard,
  SkeletonAvatar,
  LoadingButton,
  LoadingTable,
  type LoadingProps,
} from "./loading";
// Legacy Loader component (use InlineLoading directly instead)
export { default as Loader } from "../loader";

// Layout Components
export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardAction,
  CardDescription,
  CardContent,
} from "./card";

// Feedback Components
export { Alert, AlertTitle, AlertDescription } from "./alert";
export { Badge } from "./badge";
export { Skeleton } from "./skeleton";
export { Toaster } from "./sonner";

// Navigation Components
export { Tabs, TabsContent, TabsList, TabsTrigger } from "./tabs";

// Utility Components
export { Separator } from "./separator";
export { Progress } from "./progress";
export { Switch } from "./switch";
export { Avatar } from "./avatar";

// Dropdown & Select Components
export { DropdownMenu } from "./dropdown-menu";
export { Select } from "./select";

// Specialized Components
export { default as ShardLoadingAnimation } from "./shard-loading-animation";
export { default as ModelSelector } from "./model-selector";
export { default as PersonalitySelector } from "./personality-selector";
export { default as TelegramIntegration } from "./telegram-integration";
export { default as NotepadModal } from "./notepad-modal";
