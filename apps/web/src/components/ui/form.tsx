"use client";

import React, { forwardRef, createContext, useContext } from "react";
import { cn } from "@/lib/utils";
import { Label } from "./label";

// Form Context
interface FormFieldContextValue {
  id: string;
  name: string;
  error?: string;
  required?: boolean;
}

const FormFieldContext = createContext<FormFieldContextValue | null>(null);

export function useFormField() {
  const context = useContext(FormFieldContext);
  if (!context) {
    throw new Error("useFormField must be used within a FormField");
  }
  return context;
}

// Form Components
export interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  onSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
}

export const Form = forwardRef<HTMLFormElement, FormProps>(
  ({ className, onSubmit, ...props }, ref) => {
    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      onSubmit?.(e);
    };

    return (
      <form
        ref={ref}
        className={cn("space-y-6", className)}
        onSubmit={handleSubmit}
        {...props}
      />
    );
  }
);
Form.displayName = "Form";

export interface FormFieldProps {
  children: React.ReactNode;
  name: string;
  error?: string;
  required?: boolean;
  className?: string;
}

export function FormField({ children, name, error, required, className }: FormFieldProps) {
  const id = `field-${name}`;
  
  return (
    <FormFieldContext.Provider value={{ id, name, error, required }}>
      <div className={cn("space-y-2", className)}>
        {children}
      </div>
    </FormFieldContext.Provider>
  );
}

export interface FormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean;
}

export const FormLabel = forwardRef<HTMLLabelElement, FormLabelProps>(
  ({ className, required: propRequired, children, ...props }, ref) => {
    const { id, required: contextRequired } = useFormField();
    const required = propRequired ?? contextRequired;

    return (
      <Label
        ref={ref}
        htmlFor={id}
        className={cn(
          "text-sm font-medium text-app-headline",
          className
        )}
        {...props}
      >
        {children}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>
    );
  }
);
FormLabel.displayName = "FormLabel";

export interface FormControlProps {
  children: React.ReactNode;
}

export function FormControl({ children }: FormControlProps) {
  const { id, error } = useFormField();

  return (
    <div className="relative">
      {React.isValidElement(children)
        ? React.cloneElement(children as React.ReactElement<any>, {
            id,
            "aria-invalid": !!error,
            "aria-describedby": error ? `${id}-error` : undefined,
          })
        : children}
    </div>
  );
}

export interface FormDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

export const FormDescription = forwardRef<HTMLParagraphElement, FormDescriptionProps>(
  ({ className, ...props }, ref) => {
    const { id } = useFormField();
    
    return (
      <p
        ref={ref}
        id={`${id}-description`}
        className={cn("text-sm text-app-sub-headline", className)}
        {...props}
      />
    );
  }
);
FormDescription.displayName = "FormDescription";

export interface FormMessageProps extends React.HTMLAttributes<HTMLParagraphElement> {}

export const FormMessage = forwardRef<HTMLParagraphElement, FormMessageProps>(
  ({ className, children, ...props }, ref) => {
    const { error, id } = useFormField();
    const body = error || children;

    if (!body) {
      return null;
    }

    return (
      <p
        ref={ref}
        id={`${id}-error`}
        className={cn(
          "text-sm font-medium text-destructive",
          className
        )}
        {...props}
      >
        {body}
      </p>
    );
  }
);
FormMessage.displayName = "FormMessage";

// Utility function for form validation
export function validateField(value: any, rules: {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | undefined;
}): string | undefined {
  const { required, minLength, maxLength, pattern, custom } = rules;

  if (required && (!value || (typeof value === "string" && value.trim() === ""))) {
    return "This field is required";
  }

  if (value && typeof value === "string") {
    if (minLength && value.length < minLength) {
      return `Must be at least ${minLength} characters`;
    }
    
    if (maxLength && value.length > maxLength) {
      return `Must be no more than ${maxLength} characters`;
    }
    
    if (pattern && !pattern.test(value)) {
      return "Invalid format";
    }
  }

  if (custom) {
    return custom(value);
  }

  return undefined;
}

// Form hook for managing form state
export function useForm<T extends Record<string, any>>(initialValues: T) {
  const [values, setValues] = React.useState<T>(initialValues);
  const [errors, setErrors] = React.useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouchedState] = React.useState<Partial<Record<keyof T, boolean>>>({});

  const setValue = (name: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const setError = (name: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  const setTouched = (name: keyof T) => {
    setTouchedState(prev => ({ ...prev, [name]: true }));
  };

  const validate = (validationRules: Partial<Record<keyof T, Parameters<typeof validateField>[1]>>) => {
    const newErrors: Partial<Record<keyof T, string>> = {};
    
    Object.keys(validationRules).forEach(key => {
      const fieldName = key as keyof T;
      const rules = validationRules[fieldName];
      if (rules) {
        const error = validateField(values[fieldName], rules);
        if (error) {
          newErrors[fieldName] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const reset = () => {
    setValues(initialValues);
    setErrors({});
    setTouchedState({});
  };

  return {
    values,
    errors,
    touched,
    setValue,
    setError,
    setTouched,
    validate,
    reset,
  };
}
