/**
 * Component Consolidation Validation Script
 * 
 * This script validates that all consolidated components are properly exported
 * and can be imported from the centralized UI index.
 */

import { Button, buttonVariants } from "@/components/ui/button";
import { Modal, ConfirmationModal } from "@/components/ui/modal";
import { Loading, InlineLoading, PageLoading } from "@/components/ui/loading";
import PrimaryButton from "@/components/atoms/button";
import IconButton from "@/components/atoms/icon-button";

// Test centralized imports
import {
  Button as CentralizedButton,
  PrimaryButton as CentralizedPrimaryButton,
  IconButton as CentralizedIconButton,
  Modal as CentralizedModal,
  ConfirmationModal as CentralizedConfirmationModal,
  Loading as CentralizedLoading,
  InlineLoading as CentralizedInlineLoading,
  Loader as CentralizedLoader,
} from "@/components/ui";

// Validation function
function validateComponentConsolidation() {
  console.log("🔍 Validating component consolidation...");
  
  const results = {
    directImports: true,
    centralizedImports: true,
    typeExports: true,
    consistency: true,
    errors: [] as string[],
  };

  try {
    // Test direct imports exist
    if (!Button || !Modal || !Loading || !PrimaryButton || !IconButton) {
      results.directImports = false;
      results.errors.push("Direct imports failed");
    }

    // Test centralized imports exist
    if (!CentralizedButton || !CentralizedModal || !CentralizedLoading || 
        !CentralizedPrimaryButton || !CentralizedIconButton) {
      results.centralizedImports = false;
      results.errors.push("Centralized imports failed");
    }

    // Test type exports exist
    if (!buttonVariants) {
      results.typeExports = false;
      results.errors.push("Type exports failed");
    }

    // Test consistency between direct and centralized imports
    if (Button !== CentralizedButton) {
      results.consistency = false;
      results.errors.push("Button imports inconsistent");
    }

    if (Modal !== CentralizedModal) {
      results.consistency = false;
      results.errors.push("Modal imports inconsistent");
    }

    if (Loading !== CentralizedLoading) {
      results.consistency = false;
      results.errors.push("Loading imports inconsistent");
    }

    if (PrimaryButton !== CentralizedPrimaryButton) {
      results.consistency = false;
      results.errors.push("PrimaryButton imports inconsistent");
    }

    if (IconButton !== CentralizedIconButton) {
      results.consistency = false;
      results.errors.push("IconButton imports inconsistent");
    }

    // Test legacy components exist
    if (!CentralizedLoader) {
      results.errors.push("Legacy Loader component not available");
    }

    if (!CentralizedConfirmationModal || !ConfirmationModal) {
      results.errors.push("ConfirmationModal components not available");
    }

  } catch (error) {
    results.errors.push(`Validation error: ${error}`);
  }

  return results;
}

// Run validation
const results = validateComponentConsolidation();

console.log("📊 Component Consolidation Validation Results:");
console.log("✅ Direct imports:", results.directImports ? "PASS" : "FAIL");
console.log("✅ Centralized imports:", results.centralizedImports ? "PASS" : "FAIL");
console.log("✅ Type exports:", results.typeExports ? "PASS" : "FAIL");
console.log("✅ Import consistency:", results.consistency ? "PASS" : "FAIL");

if (results.errors.length > 0) {
  console.log("\n❌ Errors found:");
  results.errors.forEach((error, index) => {
    console.log(`  ${index + 1}. ${error}`);
  });
} else {
  console.log("\n🎉 All validation tests passed!");
}

// Export validation results for testing
export default results;

// Component usage examples for documentation
export const componentUsageExamples = {
  // Button examples
  basicButton: () => Button({ children: "Click me" }),
  primaryButton: () => PrimaryButton({ children: "Get Started", onClick: () => {} }),
  iconButton: () => IconButton({ "aria-label": "Settings", icon: null }),
  
  // Modal examples
  basicModal: () => Modal({ 
    isOpen: true, 
    onClose: () => {}, 
    children: "Modal content" 
  }),
  confirmationModal: () => ConfirmationModal({
    isOpen: true,
    onClose: () => {},
    onConfirm: () => {},
    title: "Confirm Action",
    message: "Are you sure?"
  }),
  
  // Loading examples
  pageLoading: () => PageLoading({ text: "Loading page..." }),
  inlineLoading: () => InlineLoading({ text: "Processing..." }),
  loadingOverlay: () => LoadingOverlay({ 
    isLoading: true, 
    children: "Content", 
    text: "Loading..." 
  }),
};

console.log("\n📚 Component usage examples available in componentUsageExamples export");