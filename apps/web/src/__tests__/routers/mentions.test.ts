/**
 * Mentions Router Tests
 * 
 * Comprehensive tests for the mentions tRPC router including:
 * - Query operations (getLatest, getPaginated)
 * - Mutation operations (create, update, delete)
 * - Rate limiting and middleware
 * - Error handling
 * - Performance monitoring
 */

import { describe, it, expect, beforeEach, beforeAll, afterAll } from "@jest/globals";
import { TRPCError } from "@trpc/server";
import { FeatureType } from "../../../prisma/generated";
import { mentionsRouter } from "@/routers/mentions";
import { 
  apiTestUtils, 
  dbTestUtils, 
  testDataFactory,
  testSetup,
  mockPerformanceMonitor 
} from "@/test-utils/testing-framework";

describe("Mentions Router", () => {
  beforeAll(async () => {
    await testSetup.beforeAll();
  });

  beforeEach(async () => {
    await testSetup.beforeEach();
  });

  afterAll(async () => {
    await testSetup.afterAll();
  });

  describe("getLatest", () => {
    it("returns latest mentions for authenticated user", async () => {
      // Setup test data
      const { user, account, mention } = await dbTestUtils.seed();
      
      const context = apiTestUtils.createMockContext({ userId: user.id });
      const input = { limit: 10 };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.getLatest,
        input,
        context
      );

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty("success", true);
      expect(result.data.mentions).toHaveLength(1);
      expect(result.data.mentions[0]).toMatchObject({
        id: mention.id,
        content: mention.content,
        authorHandle: mention.authorHandle,
        authorName: mention.authorName,
      });
    });

    it("filters out archived mentions", async () => {
      const { user, account } = await dbTestUtils.seed();
      
      // Create archived mention
      await dbTestUtils.createTestMention(user.id, account.id, { 
        archived: true,
        content: "Archived mention" 
      });
      
      // Create active mention
      await dbTestUtils.createTestMention(user.id, account.id, { 
        archived: false,
        content: "Active mention" 
      });

      const context = apiTestUtils.createMockContext({ userId: user.id });
      const result = await apiTestUtils.testProcedure(
        mentionsRouter.getLatest,
        { limit: 10 },
        context
      );

      expect(result.success).toBe(true);
      expect(result.data.mentions).toHaveLength(2); // One from seed + one active
      expect(result.data.mentions.every((m: any) => !m.archived)).toBe(true);
    });

    it("respects limit parameter", async () => {
      const { user, account } = await dbTestUtils.seed();
      
      // Create multiple mentions
      for (let i = 0; i < 5; i++) {
        await dbTestUtils.createTestMention(user.id, account.id, {
          content: `Mention ${i}`,
        });
      }

      const context = apiTestUtils.createMockContext({ userId: user.id });
      const result = await apiTestUtils.testProcedure(
        mentionsRouter.getLatest,
        { limit: 3 },
        context
      );

      expect(result.success).toBe(true);
      expect(result.data.mentions).toHaveLength(3);
    });

    it("requires authentication", async () => {
      const context = apiTestUtils.createMockContext({ userId: null });
      const result = await apiTestUtils.testProcedure(
        mentionsRouter.getLatest,
        { limit: 10 },
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TRPCError);
      expect(result.error.code).toBe("UNAUTHORIZED");
    });

    it("tracks performance metrics", async () => {
      const { user } = await dbTestUtils.seed();
      const context = apiTestUtils.createMockContext({ userId: user.id });

      await apiTestUtils.testProcedure(
        mentionsRouter.getLatest,
        { limit: 10 },
        context
      );

      expect(mockPerformanceMonitor.trackUserAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          type: "user_action",
          action: expect.stringContaining("mentions"),
        })
      );
    });
  });

  describe("getPaginated", () => {
    it("returns paginated mentions with cursor", async () => {
      const { user, account } = await dbTestUtils.seed();
      
      // Create multiple mentions with different timestamps
      const mentions = [];
      for (let i = 0; i < 5; i++) {
        const mention = await dbTestUtils.createTestMention(user.id, account.id, {
          content: `Mention ${i}`,
          mentionedAt: new Date(Date.now() - i * 1000 * 60), // 1 minute apart
        });
        mentions.push(mention);
      }

      const context = apiTestUtils.createMockContext({ userId: user.id });
      const result = await apiTestUtils.testProcedure(
        mentionsRouter.getPaginated,
        { 
          limit: 3,
          accountId: account.id,
        },
        context
      );

      expect(result.success).toBe(true);
      expect(result.data.mentions).toHaveLength(3);
      expect(result.data).toHaveProperty("nextCursor");
      expect(result.data).toHaveProperty("hasNextPage");
    });

    it("filters by account when specified", async () => {
      const { user } = await dbTestUtils.seed();
      
      // Create second account
      const account2 = await dbTestUtils.testPrisma.monitoredAccount.create({
        data: testDataFactory.account({ 
          userId: user.id,
          twitterHandle: "account2" 
        }),
      });

      // Create mentions for both accounts
      await dbTestUtils.createTestMention(user.id, account2.id, {
        content: "Account 2 mention",
      });

      const context = apiTestUtils.createMockContext({ userId: user.id });
      const result = await apiTestUtils.testProcedure(
        mentionsRouter.getPaginated,
        { 
          limit: 10,
          accountId: account2.id,
        },
        context
      );

      expect(result.success).toBe(true);
      expect(result.data.mentions).toHaveLength(1);
      expect(result.data.mentions[0].content).toBe("Account 2 mention");
    });

    it("handles cursor-based pagination", async () => {
      const { user, account } = await dbTestUtils.seed();
      
      // Create mentions with known timestamps
      const baseTime = Date.now();
      for (let i = 0; i < 5; i++) {
        await dbTestUtils.createTestMention(user.id, account.id, {
          content: `Mention ${i}`,
          mentionedAt: new Date(baseTime - i * 1000 * 60),
        });
      }

      const context = apiTestUtils.createMockContext({ userId: user.id });
      
      // First page
      const firstPage = await apiTestUtils.testProcedure(
        mentionsRouter.getPaginated,
        { limit: 2 },
        context
      );

      expect(firstPage.success).toBe(true);
      expect(firstPage.data.mentions).toHaveLength(2);
      expect(firstPage.data.hasNextPage).toBe(true);

      // Second page with cursor
      const secondPage = await apiTestUtils.testProcedure(
        mentionsRouter.getPaginated,
        { 
          limit: 2,
          cursor: firstPage.data.nextCursor,
        },
        context
      );

      expect(secondPage.success).toBe(true);
      expect(secondPage.data.mentions).toHaveLength(2);
      
      // Ensure no overlap
      const firstPageIds = firstPage.data.mentions.map((m: any) => m.id);
      const secondPageIds = secondPage.data.mentions.map((m: any) => m.id);
      expect(firstPageIds).not.toEqual(expect.arrayContaining(secondPageIds));
    });
  });

  describe("createFromUrl", () => {
    it("creates mention from Twitter URL", async () => {
      const { user, account } = await dbTestUtils.seed();
      
      const context = apiTestUtils.createMockContext({ userId: user.id });
      const input = {
        url: "https://twitter.com/testuser/status/*********",
        accountId: account.id,
      };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.createFromUrl,
        input,
        context
      );

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty("success", true);
      expect(result.data).toHaveProperty("mention");
      expect(result.data.mention).toMatchObject({
        link: input.url,
        accountId: account.id,
      });
    });

    it("validates Twitter URL format", async () => {
      const { user, account } = await dbTestUtils.seed();
      
      const context = apiTestUtils.createMockContext({ userId: user.id });
      const input = {
        url: "https://invalid-url.com",
        accountId: account.id,
      };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.createFromUrl,
        input,
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TRPCError);
      expect(result.error.code).toBe("BAD_REQUEST");
    });

    it("checks rate limits for mention creation", async () => {
      const { user, account } = await dbTestUtils.seed();
      
      // Mock rate limit exceeded
      const context = apiTestUtils.createMockContext({ 
        userId: user.id,
        rateLimitExceeded: true,
      });
      
      const input = {
        url: "https://twitter.com/testuser/status/*********",
        accountId: account.id,
      };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.createFromUrl,
        input,
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TRPCError);
      expect(result.error.code).toBe("TOO_MANY_REQUESTS");
    });

    it("records usage after successful creation", async () => {
      const { user, account } = await dbTestUtils.seed();
      
      const context = apiTestUtils.createMockContext({ userId: user.id });
      const input = {
        url: "https://twitter.com/testuser/status/*********",
        accountId: account.id,
      };

      await apiTestUtils.testProcedure(
        mentionsRouter.createFromUrl,
        input,
        context
      );

      expect(mockPerformanceMonitor.recordBusinessMetric).toHaveBeenCalledWith(
        expect.objectContaining({
          type: "feature_adoption",
          name: expect.stringContaining("mention"),
        })
      );
    });
  });

  describe("updateBullishScore", () => {
    it("updates bullish score for mention", async () => {
      const { user, mention } = await dbTestUtils.seed();
      
      const context = apiTestUtils.createMockContext({ userId: user.id });
      const input = {
        mentionId: mention.id,
        bullishScore: 85,
      };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.updateBullishScore,
        input,
        context
      );

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty("success", true);

      // Verify database update
      const updatedMention = await dbTestUtils.testPrisma.mention.findUnique({
        where: { id: mention.id },
      });
      expect(updatedMention?.bullishScore).toBe(85);
    });

    it("validates bullish score range", async () => {
      const { user, mention } = await dbTestUtils.seed();
      
      const context = apiTestUtils.createMockContext({ userId: user.id });
      const input = {
        mentionId: mention.id,
        bullishScore: 150, // Invalid: > 100
      };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.updateBullishScore,
        input,
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TRPCError);
      expect(result.error.code).toBe("BAD_REQUEST");
    });

    it("prevents updating mentions from other users", async () => {
      const { mention } = await dbTestUtils.seed();
      const otherUser = await dbTestUtils.createTestUser({ 
        email: "<EMAIL>" 
      });
      
      const context = apiTestUtils.createMockContext({ userId: otherUser.id });
      const input = {
        mentionId: mention.id,
        bullishScore: 85,
      };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.updateBullishScore,
        input,
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TRPCError);
      expect(result.error.code).toBe("NOT_FOUND");
    });
  });

  describe("deleteMention", () => {
    it("deletes mention for authenticated user", async () => {
      const { user, mention } = await dbTestUtils.seed();
      
      const context = apiTestUtils.createMockContext({ userId: user.id });
      const input = { mentionId: mention.id };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.deleteMention,
        input,
        context
      );

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty("success", true);

      // Verify deletion
      const deletedMention = await dbTestUtils.testPrisma.mention.findUnique({
        where: { id: mention.id },
      });
      expect(deletedMention).toBeNull();
    });

    it("prevents deleting mentions from other users", async () => {
      const { mention } = await dbTestUtils.seed();
      const otherUser = await dbTestUtils.createTestUser({ 
        email: "<EMAIL>" 
      });
      
      const context = apiTestUtils.createMockContext({ userId: otherUser.id });
      const input = { mentionId: mention.id };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.deleteMention,
        input,
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TRPCError);
      expect(result.error.code).toBe("NOT_FOUND");
    });

    it("handles non-existent mention gracefully", async () => {
      const { user } = await dbTestUtils.seed();
      
      const context = apiTestUtils.createMockContext({ userId: user.id });
      const input = { mentionId: "non-existent-id" };

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.deleteMention,
        input,
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TRPCError);
      expect(result.error.code).toBe("NOT_FOUND");
    });
  });

  describe("Error Handling", () => {
    it("handles database connection errors", async () => {
      // Mock database error
      const context = apiTestUtils.createMockContext({ 
        userId: "test-user",
        prisma: {
          mention: {
            findMany: jest.fn().mockRejectedValue(new Error("Database connection failed")),
          },
        },
      });

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.getLatest,
        { limit: 10 },
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TRPCError);
      expect(result.error.code).toBe("INTERNAL_SERVER_ERROR");
    });

    it("handles validation errors properly", async () => {
      const { user } = await dbTestUtils.seed();
      const context = apiTestUtils.createMockContext({ userId: user.id });

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.getLatest,
        { limit: -1 }, // Invalid limit
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TRPCError);
      expect(result.error.code).toBe("BAD_REQUEST");
    });
  });

  describe("Performance", () => {
    it("handles large datasets efficiently", async () => {
      const { user, account } = await dbTestUtils.seed();
      
      // Create many mentions
      const mentions = [];
      for (let i = 0; i < 100; i++) {
        mentions.push(testDataFactory.mention({
          userId: user.id,
          accountId: account.id,
          content: `Mention ${i}`,
        }));
      }
      
      await dbTestUtils.testPrisma.mention.createMany({
        data: mentions,
      });

      const context = apiTestUtils.createMockContext({ userId: user.id });
      const startTime = performance.now();

      const result = await apiTestUtils.testProcedure(
        mentionsRouter.getLatest,
        { limit: 50 },
        context
      );

      const duration = performance.now() - startTime;

      expect(result.success).toBe(true);
      expect(duration).toBeLessThan(1000); // Should complete in under 1 second
    });
  });
});
