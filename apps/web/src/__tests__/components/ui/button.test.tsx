/**
 * Button Component Tests
 * 
 * Comprehensive tests for the unified button system including:
 * - Basic rendering and props
 * - Loading states
 * - Icon positioning
 * - Accessibility
 * - Performance
 */

import { describe, it, expect, beforeEach } from "@jest/globals";
import { But<PERSON> } from "@/components/ui/button";
import IconButton from "@/components/atoms/icon-button";
import PrimaryButton from "@/components/atoms/button";
import { 
  componentTestUtils, 
  performanceTestUtils,
  testSetup 
} from "@/test-utils/testing-framework";
import { MdArrowOutward } from "react-icons/md";

describe("Button Components", () => {
  beforeEach(async () => {
    await testSetup.beforeEach();
  });

  describe("Button (Unified)", () => {
    it("renders with default props", () => {
      componentTestUtils.renderWithProviders(
        <Button>Click me</Button>
      );

      const button = componentTestUtils.screen.getByRole("button", { name: /click me/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass("inline-flex", "items-center", "justify-center");
    });

    it("applies variant classes correctly", () => {
      const { rerender } = componentTestUtils.renderWithProviders(
        <Button variant="primary">Primary</Button>
      );

      let button = componentTestUtils.screen.getByRole("button");
      expect(button).toHaveClass("bg-app-headline");

      rerender(<Button variant="destructive">Destructive</Button>);
      button = componentTestUtils.screen.getByRole("button");
      expect(button).toHaveClass("bg-destructive");
    });

    it("shows loading state correctly", () => {
      componentTestUtils.renderWithProviders(
        <Button loading loadingText="Processing...">
          Submit
        </Button>
      );

      const button = componentTestUtils.screen.getByRole("button");
      expect(button).toBeDisabled();
      expect(componentTestUtils.screen.getByText("Processing...")).toBeInTheDocument();
      expect(componentTestUtils.screen.getByTestId("loading-spinner")).toBeInTheDocument();
    });

    it("handles icon positioning", () => {
      const { rerender } = componentTestUtils.renderWithProviders(
        <Button icon={<MdArrowOutward />} iconPosition="left">
          With Icon
        </Button>
      );

      let button = componentTestUtils.screen.getByRole("button");
      let icon = button.querySelector("svg");
      expect(icon).toBeInTheDocument();
      expect(icon?.parentElement).toHaveClass("mr-2");

      rerender(
        <Button icon={<MdArrowOutward />} iconPosition="right">
          With Icon
        </Button>
      );

      button = componentTestUtils.screen.getByRole("button");
      icon = button.querySelector("svg");
      expect(icon?.parentElement).toHaveClass("ml-2");
    });

    it("is accessible", () => {
      componentTestUtils.renderWithProviders(
        <Button aria-label="Submit form" disabled>
          Submit
        </Button>
      );

      const button = componentTestUtils.screen.getByRole("button", { name: /submit form/i });
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute("aria-label", "Submit form");
    });

    it("handles click events", async () => {
      const handleClick = jest.fn();
      componentTestUtils.renderWithProviders(
        <Button onClick={handleClick}>Click me</Button>
      );

      const button = componentTestUtils.screen.getByRole("button");
      await componentTestUtils.userInteraction.click(button);

      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it("prevents click when loading", async () => {
      const handleClick = jest.fn();
      componentTestUtils.renderWithProviders(
        <Button onClick={handleClick} loading>
          Loading
        </Button>
      );

      const button = componentTestUtils.screen.getByRole("button");
      await componentTestUtils.userInteraction.click(button);

      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe("IconButton", () => {
    it("renders with icon", () => {
      componentTestUtils.renderWithProviders(
        <IconButton icon={MdArrowOutward} aria-label="Arrow button" />
      );

      const button = componentTestUtils.screen.getByRole("button", { name: /arrow button/i });
      expect(button).toBeInTheDocument();
      expect(button.querySelector("svg")).toBeInTheDocument();
    });

    it("applies variant styles", () => {
      componentTestUtils.renderWithProviders(
        <IconButton 
          icon={MdArrowOutward} 
          variant="appSecondary" 
          aria-label="Secondary icon" 
        />
      );

      const button = componentTestUtils.screen.getByRole("button");
      expect(button).toHaveClass("rounded-full");
    });

    it("handles custom className", () => {
      componentTestUtils.renderWithProviders(
        <IconButton 
          icon={MdArrowOutward} 
          className="custom-class" 
          aria-label="Custom icon" 
        />
      );

      const button = componentTestUtils.screen.getByRole("button");
      expect(button).toHaveClass("custom-class", "rounded-full");
    });
  });

  describe("PrimaryButton", () => {
    it("renders with children and icon", () => {
      componentTestUtils.renderWithProviders(
        <PrimaryButton onClick={() => {}}>
          Get Started
        </PrimaryButton>
      );

      const button = componentTestUtils.screen.getByRole("button", { name: /get started/i });
      expect(button).toBeInTheDocument();
      expect(button.querySelector("svg")).toBeInTheDocument(); // Default arrow icon
    });

    it("uses custom icon when provided", () => {
      componentTestUtils.renderWithProviders(
        <PrimaryButton icon={MdArrowOutward} onClick={() => {}}>
          Custom Icon
        </PrimaryButton>
      );

      const button = componentTestUtils.screen.getByRole("button");
      expect(button.querySelector("svg")).toBeInTheDocument();
    });

    it("applies primary variant by default", () => {
      componentTestUtils.renderWithProviders(
        <PrimaryButton onClick={() => {}}>
          Primary
        </PrimaryButton>
      );

      const button = componentTestUtils.screen.getByRole("button");
      expect(button).toHaveClass("min-w-[240px]", "rounded-[100px]");
    });

    it("handles disabled state", () => {
      componentTestUtils.renderWithProviders(
        <PrimaryButton onClick={() => {}} disabled>
          Disabled
        </PrimaryButton>
      );

      const button = componentTestUtils.screen.getByRole("button");
      expect(button).toBeDisabled();
    });
  });

  describe("Performance", () => {
    it("renders quickly", async () => {
      const renderTime = await performanceTestUtils.measureRenderTime(() => {
        componentTestUtils.renderWithProviders(
          <Button>Performance Test</Button>
        );
      });

      // Button should render in less than 50ms
      expect(renderTime).toBeLessThan(50);
    });

    it("handles rapid state changes efficiently", async () => {
      let renderCount = 0;
      const TestComponent = () => {
        renderCount++;
        const [loading, setLoading] = React.useState(false);
        
        React.useEffect(() => {
          const interval = setInterval(() => {
            setLoading(prev => !prev);
          }, 10);
          
          setTimeout(() => clearInterval(interval), 100);
          return () => clearInterval(interval);
        }, []);

        return <Button loading={loading}>Toggle Loading</Button>;
      };

      componentTestUtils.renderWithProviders(<TestComponent />);
      
      await componentTestUtils.waitFor(() => {
        // Should not re-render excessively
        expect(renderCount).toBeLessThan(20);
      }, { timeout: 200 });
    });
  });

  describe("Accessibility", () => {
    it("supports keyboard navigation", async () => {
      const handleClick = jest.fn();
      componentTestUtils.renderWithProviders(
        <Button onClick={handleClick}>Keyboard Test</Button>
      );

      const button = componentTestUtils.screen.getByRole("button");
      button.focus();
      
      expect(button).toHaveFocus();
      
      // Simulate Enter key
      componentTestUtils.fireEvent.keyDown(button, { key: "Enter", code: "Enter" });
      expect(handleClick).toHaveBeenCalledTimes(1);
      
      // Simulate Space key
      componentTestUtils.fireEvent.keyDown(button, { key: " ", code: "Space" });
      expect(handleClick).toHaveBeenCalledTimes(2);
    });

    it("has proper ARIA attributes", () => {
      componentTestUtils.renderWithProviders(
        <Button 
          aria-label="Submit form" 
          aria-describedby="form-help"
          disabled
        >
          Submit
        </Button>
      );

      const button = componentTestUtils.screen.getByRole("button");
      expect(button).toHaveAttribute("aria-label", "Submit form");
      expect(button).toHaveAttribute("aria-describedby", "form-help");
      expect(button).toHaveAttribute("disabled");
    });

    it("announces loading state to screen readers", () => {
      componentTestUtils.renderWithProviders(
        <Button loading loadingText="Submitting form">
          Submit
        </Button>
      );

      const button = componentTestUtils.screen.getByRole("button");
      expect(button).toHaveAttribute("disabled");
      expect(componentTestUtils.screen.getByText("Submitting form")).toBeInTheDocument();
    });
  });

  describe("Integration", () => {
    it("works with form submission", async () => {
      const handleSubmit = jest.fn();
      
      componentTestUtils.renderWithProviders(
        <form onSubmit={handleSubmit}>
          <Button type="submit">Submit Form</Button>
        </form>
      );

      const button = componentTestUtils.screen.getByRole("button", { name: /submit form/i });
      await componentTestUtils.userInteraction.click(button);

      expect(handleSubmit).toHaveBeenCalledTimes(1);
    });

    it("integrates with loading states from async operations", async () => {
      const TestComponent = () => {
        const [loading, setLoading] = React.useState(false);
        
        const handleAsync = async () => {
          setLoading(true);
          await new Promise(resolve => setTimeout(resolve, 100));
          setLoading(false);
        };

        return (
          <Button onClick={handleAsync} loading={loading}>
            Async Action
          </Button>
        );
      };

      componentTestUtils.renderWithProviders(<TestComponent />);

      const button = componentTestUtils.screen.getByRole("button");
      await componentTestUtils.userInteraction.click(button);

      // Should show loading state
      expect(button).toBeDisabled();
      
      // Should return to normal state
      await componentTestUtils.waitFor(() => {
        expect(button).not.toBeDisabled();
      });
    });
  });
});
