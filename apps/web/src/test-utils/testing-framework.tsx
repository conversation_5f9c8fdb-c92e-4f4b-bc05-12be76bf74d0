/**
 * Comprehensive Testing Framework for BuddyChip
 * 
 * Provides utilities for testing React components, API endpoints, database operations,
 * and performance monitoring with proper mocking and isolation.
 */

import React from "react";
import { render, screen, fireEvent, waitFor, type RenderOptions } from "@testing-library/react";
import { renderHook, type RenderHookOptions } from "@testing-library/react-hooks";
import userEvent from "@testing-library/user-event";
import { jest } from "@jest/globals";
import { PrismaClient } from "../../prisma/generated";
import { createPrismaClient } from "../lib/prisma-config";
import { enhancedPerformanceMonitor } from "../lib/enhanced-performance-monitor";
import { analytics } from "../lib/user-analytics";

// Test database client
export const testPrisma = createPrismaClient({
  instanceId: "test",
  databaseUrl: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL,
  disableMiddleware: true,
  logLevels: ["error"],
});

// Mock implementations
export const mockAnalytics = {
  featureStarted: jest.fn(),
  featureCompleted: jest.fn(),
  featureFailed: jest.fn(),
  userSignedUp: jest.fn(),
  userSubscribed: jest.fn(),
  pageViewed: jest.fn(),
  errorOccurred: jest.fn(),
  identifyUser: jest.fn(),
  getSessionSummary: jest.fn(() => ({
    sessionId: "test-session",
    userId: "test-user",
    duration: 0,
    pageViews: 0,
    uniquePages: 0,
    actions: 0,
    journey: [],
  })),
};

export const mockPerformanceMonitor = {
  trackUserAnalytics: jest.fn(),
  recordBusinessMetric: jest.fn(),
  recordWebVital: jest.fn(),
  createAlert: jest.fn(),
  getPerformanceReport: jest.fn(() => ({
    summary: {
      timeWindow: 60,
      webVitalsCount: 0,
      analyticsEventsCount: 0,
      businessMetricsCount: 0,
      alertsCount: 0,
      criticalAlertsCount: 0,
    },
    webVitals: [],
    analytics: {
      eventsByType: {},
      uniquePages: 0,
      errorRate: 0,
      topPages: [],
    },
    businessMetrics: [],
    alerts: [],
    serverMetrics: {
      averageDuration: 0,
      slowQueries: [],
      queryCount: 0,
      topSlowQueries: [],
    },
  })),
};

// Test data factories
export const testDataFactory = {
  user: (overrides: Partial<any> = {}) => ({
    id: "test-user-id",
    email: "<EMAIL>",
    name: "Test User",
    avatarUrl: "https://example.com/avatar.jpg",
    planId: "free-plan",
    personalityId: null,
    customSystemPrompt: null,
    useFirstPerson: false,
    createdAt: new Date(),
    lastActiveAt: new Date(),
    ...overrides,
  }),

  mention: (overrides: Partial<any> = {}) => ({
    id: "test-mention-id",
    content: "This is a test mention about @buddychip",
    authorName: "Test Author",
    authorHandle: "testauthor",
    authorAvatarUrl: "https://example.com/author.jpg",
    link: "https://twitter.com/testauthor/status/*********",
    mentionedAt: new Date(),
    createdAt: new Date(),
    bullishScore: 75,
    importanceScore: 80,
    keywords: ["buddychip", "test"],
    processed: false,
    archived: false,
    userId: "test-user-id",
    accountId: "test-account-id",
    ...overrides,
  }),

  account: (overrides: Partial<any> = {}) => ({
    id: "test-account-id",
    twitterHandle: "testaccount",
    displayName: "Test Account",
    avatarUrl: "https://example.com/account.jpg",
    isActive: true,
    lastSyncAt: new Date(),
    totalMentions: 10,
    syncSettings: {
      syncMentions: true,
      syncUserTweets: false,
      syncReplies: false,
      syncRetweets: true,
    },
    userId: "test-user-id",
    createdAt: new Date(),
    ...overrides,
  }),

  aiResponse: (overrides: Partial<any> = {}) => ({
    id: "test-response-id",
    content: "This is a test AI response",
    model: "gpt-4",
    mentionId: "test-mention-id",
    createdAt: new Date(),
    ...overrides,
  }),
};

// Database test utilities
export const dbTestUtils = {
  /**
   * Clean up test database
   */
  async cleanup() {
    await testPrisma.aIResponse.deleteMany();
    await testPrisma.mention.deleteMany();
    await testPrisma.monitoredAccount.deleteMany();
    await testPrisma.usageLog.deleteMany();
    await testPrisma.user.deleteMany();
  },

  /**
   * Seed test data
   */
  async seed() {
    const user = await testPrisma.user.create({
      data: testDataFactory.user(),
    });

    const account = await testPrisma.monitoredAccount.create({
      data: testDataFactory.account({ userId: user.id }),
    });

    const mention = await testPrisma.mention.create({
      data: testDataFactory.mention({ 
        userId: user.id, 
        accountId: account.id 
      }),
    });

    const response = await testPrisma.aIResponse.create({
      data: testDataFactory.aiResponse({ mentionId: mention.id }),
    });

    return { user, account, mention, response };
  },

  /**
   * Create test user with full setup
   */
  async createTestUser(overrides: Partial<any> = {}) {
    return await testPrisma.user.create({
      data: testDataFactory.user(overrides),
    });
  },

  /**
   * Create test mention with relations
   */
  async createTestMention(userId: string, accountId: string, overrides: Partial<any> = {}) {
    return await testPrisma.mention.create({
      data: testDataFactory.mention({ 
        userId, 
        accountId, 
        ...overrides 
      }),
    });
  },
};

// Component testing utilities
export const componentTestUtils = {
  /**
   * Render component with providers
   */
  renderWithProviders: (ui: React.ReactElement, options: RenderOptions = {}) => {
    const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
      return (
        <div data-testid="test-providers">
          {children}
        </div>
      );
    };

    return render(ui, { wrapper: AllTheProviders, ...options });
  },

  /**
   * Render hook with providers
   */
  renderHookWithProviders: <TProps, TResult>(
    hook: (props: TProps) => TResult,
    options: RenderHookOptions<TProps> = {}
  ) => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <div data-testid="test-hook-providers">{children}</div>
    );

    return renderHook(hook, { wrapper, ...options });
  },

  /**
   * Wait for element to appear
   */
  waitForElement: async (testId: string, timeout = 5000) => {
    return await waitFor(
      () => {
        const element = screen.getByTestId(testId);
        expect(element).toBeInTheDocument();
        return element;
      },
      { timeout }
    );
  },

  /**
   * Simulate user interaction
   */
  userInteraction: {
    click: async (element: HTMLElement) => {
      const user = userEvent.setup();
      await user.click(element);
    },

    type: async (element: HTMLElement, text: string) => {
      const user = userEvent.setup();
      await user.type(element, text);
    },

    selectOption: async (element: HTMLElement, option: string) => {
      const user = userEvent.setup();
      await user.selectOptions(element, option);
    },
  },
};

// API testing utilities
export const apiTestUtils = {
  /**
   * Mock tRPC context
   */
  createMockContext: (overrides: Partial<any> = {}) => ({
    prisma: testPrisma,
    userId: "test-user-id",
    req: {
      headers: new Map([
        ["user-agent", "test-agent"],
        ["origin", "http://localhost:3000"],
      ]),
    },
    ...overrides,
  }),

  /**
   * Test tRPC procedure
   */
  testProcedure: async (
    procedure: any,
    input: any,
    context: any = {}
  ) => {
    const ctx = apiTestUtils.createMockContext(context);
    
    try {
      const result = await procedure({ input, ctx });
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error };
    }
  },

  /**
   * Mock external API calls
   */
  mockExternalAPIs: () => {
    // Mock Twitter API
    global.fetch = jest.fn().mockImplementation((url: string) => {
      if (url.includes("api.twitterapi.io")) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            tweets: [testDataFactory.mention()],
            has_next_page: false,
            next_cursor: null,
          }),
        });
      }

      // Mock Cookie.fun API
      if (url.includes("api.staging.cookie.fun")) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            ok: {
              entries: [
                {
                  projectId: "test-project",
                  projectName: "Test Project",
                  projectSlug: "test-project",
                  mindshare: 1000,
                  mindshareDelta: 50,
                },
              ],
            },
          }),
        });
      }

      return Promise.reject(new Error(`Unmocked API call: ${url}`));
    });
  },
};

// Performance testing utilities
export const performanceTestUtils = {
  /**
   * Mock performance monitoring
   */
  mockPerformanceMonitoring: () => {
    jest.spyOn(enhancedPerformanceMonitor, "trackUserAnalytics").mockImplementation(mockPerformanceMonitor.trackUserAnalytics);
    jest.spyOn(enhancedPerformanceMonitor, "recordBusinessMetric").mockImplementation(mockPerformanceMonitor.recordBusinessMetric);
    jest.spyOn(enhancedPerformanceMonitor, "getPerformanceReport").mockImplementation(mockPerformanceMonitor.getPerformanceReport);
  },

  /**
   * Mock analytics
   */
  mockAnalyticsTracking: () => {
    Object.keys(mockAnalytics).forEach(key => {
      jest.spyOn(analytics, key as keyof typeof analytics).mockImplementation(mockAnalytics[key as keyof typeof mockAnalytics]);
    });
  },

  /**
   * Measure component render time
   */
  measureRenderTime: async (renderFn: () => void) => {
    const start = performance.now();
    renderFn();
    await waitFor(() => {}, { timeout: 100 }); // Wait for render
    const end = performance.now();
    return end - start;
  },

  /**
   * Test memory usage
   */
  measureMemoryUsage: () => {
    if (typeof window !== "undefined" && "memory" in performance) {
      return (performance as any).memory;
    }
    return null;
  },
};

// Test setup and teardown
export const testSetup = {
  /**
   * Setup before all tests
   */
  beforeAll: async () => {
    // Connect to test database
    await testPrisma.$connect();
    
    // Mock external APIs
    apiTestUtils.mockExternalAPIs();
    
    // Mock performance monitoring
    performanceTestUtils.mockPerformanceMonitoring();
    performanceTestUtils.mockAnalyticsTracking();
  },

  /**
   * Setup before each test
   */
  beforeEach: async () => {
    // Clean database
    await dbTestUtils.cleanup();
    
    // Reset mocks
    jest.clearAllMocks();
  },

  /**
   * Cleanup after all tests
   */
  afterAll: async () => {
    // Clean database
    await dbTestUtils.cleanup();
    
    // Disconnect from database
    await testPrisma.$disconnect();
    
    // Restore mocks
    jest.restoreAllMocks();
  },
};

// Export all utilities
export {
  render,
  screen,
  fireEvent,
  waitFor,
  userEvent,
  renderHook,
  jest,
};

// Re-export testing library utilities with our custom ones
export * from "@testing-library/react";
export * from "@testing-library/react-hooks";
export * from "@testing-library/user-event";
