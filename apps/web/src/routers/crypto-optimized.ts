/**
 * Optimized Crypto Router for BuddyChip tRPC API
 *
 * This router is optimized with service layer integration, comprehensive
 * error handling, performance monitoring, and advanced caching strategies.
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";
import { getServiceRegistry } from "../services";

// Input validation schemas
const smartFollowersSchema = z.object({
  username: z.string().min(1, "Username is required").optional(),
  userId: z.string().optional(),
  limit: z.number().min(1).max(50).default(20),
});

const accountFeedSchema = z.object({
  username: z.string().min(1, "Username is required").optional(),
  userId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  type: z.enum(["Original", "Reply", "Quote"]).optional(),
  hasMedia: z.boolean().optional(),
  sortBy: z.enum(["CreatedAt", "Impressions"]).optional(),
  sortOrder: z.enum(["Ascending", "Descending"]).optional(),
  limit: z.number().min(1).max(20).default(10),
});

const searchProjectsSchema = z.object({
  searchQuery: z.string().optional(),
  projectSlug: z.string().optional(),
  sectorSlug: z.string().optional(),
  type: z.enum(["Original", "Reply", "Quote"]).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  sortBy: z.enum([
    "SmartEngagementPoints",
    "Impressions",
    "MatchingTweetsCount",
    "Mindshare",
  ]).optional(),
  sortOrder: z.enum(["Ascending", "Descending"]).optional(),
  mindshareTimeframe: z.enum(["_7Days", "_30Days"]).optional(),
  granulation: z.enum(["_1Hour", "_24Hours"]).optional(),
  metricType: z.enum(["Impressions", "EngagementRate", "Mentions"]).optional(),
  limit: z.number().min(1).max(20).default(10),
});

const trendingProjectsSchema = z.object({
  sectorSlug: z.string().optional(),
  timeframe: z.enum(["_7Days", "_30Days"]).default("_7Days"),
  limit: z.number().min(1).max(20).default(10),
});

const projectMetricsSchema = z.object({
  projectSlug: z.string().min(1, "Project slug is required"),
  metricType: z.enum(["Impressions", "EngagementRate", "Mentions"]),
  granulation: z.enum(["_1Hour", "_24Hours"]),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

const competitiveAnalysisSchema = z.object({
  projectSlug: z.string().min(1, "Project slug is required"),
});

const sectorInfluencersSchema = z.object({
  username: z.string().min(1, "Username is required"),
  targetSector: z.string().min(1, "Target sector is required"),
  limit: z.number().min(1).max(50).default(10),
});

const marketIntelligenceSchema = z.object({
  keywords: z.array(z.string()).optional(),
  sectorSlug: z.string().optional(),
  projectSlugs: z.array(z.string()).optional(),
  timeframe: z.enum(["_7Days", "_30Days"]).default("_7Days"),
});

const cacheClearSchema = z.object({
  type: z.enum(["sectors", "trending", "all"]).default("all"),
  sectorSlug: z.string().optional(),
  timeframe: z.string().optional(),
});

/**
 * Helper function to handle service results and convert to tRPC responses
 */
function handleServiceResult<T>(result: any, operation: string): T {
  if (result.success) {
    return result.data;
  }
  
  // Log the error for monitoring
  console.error(`❌ ${operation} failed:`, result.error);
  
  // Determine appropriate tRPC error code
  let code: any = "INTERNAL_SERVER_ERROR";
  
  if (result.error?.includes('Invalid')) {
    code = "BAD_REQUEST";
  } else if (result.error?.includes('not found')) {
    code = "NOT_FOUND";
  } else if (result.error?.includes('limit exceeded') || result.error?.includes('forbidden')) {
    code = "FORBIDDEN";
  } else if (result.error?.includes('unavailable')) {
    code = "SERVICE_UNAVAILABLE";
  } else if (result.error?.includes('required')) {
    code = "BAD_REQUEST";
  }
  
  throw new TRPCError({
    code,
    message: result.error || "Operation failed",
  });
}

/**
 * Validation helper for username or userId requirement
 */
function validateUsernameOrUserId(input: { username?: string; userId?: string }) {
  if (!input.username && !input.userId) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Either username or userId must be provided",
    });
  }
}

/**
 * Validation helper for search requirements
 */
function validateSearchRequirements(input: { searchQuery?: string; projectSlug?: string }) {
  if (!input.searchQuery && !input.projectSlug) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "At least one of searchQuery or projectSlug must be provided",
    });
  }
}

export const cryptoOptimizedRouter = createTRPCRouter({
  /**
   * Test Cookie.fun API connection
   */
  testConnection: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma);
    const cryptoService = registry.getCryptoService();
    
    const result = await cryptoService.testConnection(ctx.userId!);
    return handleServiceResult(result, 'testConnection');
  }),

  /**
   * Get all available crypto sectors (with multi-layer caching)
   */
  getSectors: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma);
    const cryptoService = registry.getCryptoService();
    
    const result = await cryptoService.getSectors(ctx.userId!);
    return handleServiceResult(result, 'getSectors');
  }),

  /**
   * Get smart followers for a Twitter account
   */
  getSmartFollowers: protectedProcedure
    .input(smartFollowersSchema)
    .mutation(async ({ input, ctx }) => {
      validateUsernameOrUserId(input);
      
      const registry = getServiceRegistry(ctx.prisma);
      const cryptoService = registry.getCryptoService();
      
      const result = await cryptoService.getSmartFollowers(ctx.userId!, input);
      return handleServiceResult(result, 'getSmartFollowers');
    }),

  /**
   * Get account feed with filtering options
   */
  getAccountFeed: protectedProcedure
    .input(accountFeedSchema)
    .mutation(async ({ input, ctx }) => {
      validateUsernameOrUserId(input);
      
      const registry = getServiceRegistry(ctx.prisma);
      const cryptoService = registry.getCryptoService();
      
      const result = await cryptoService.getAccountFeed(ctx.userId!, input);
      return handleServiceResult(result, 'getAccountFeed');
    }),

  /**
   * Search for crypto projects with various filters
   */
  searchProjects: protectedProcedure
    .input(searchProjectsSchema)
    .mutation(async ({ input, ctx }) => {
      validateSearchRequirements(input);
      
      const registry = getServiceRegistry(ctx.prisma);
      const cryptoService = registry.getCryptoService();
      
      const result = await cryptoService.searchProjects(ctx.userId!, input);
      return handleServiceResult(result, 'searchProjects');
    }),

  /**
   * Get trending projects in a specific sector (with multi-layer caching)
   */
  getTrendingProjects: protectedProcedure
    .input(trendingProjectsSchema)
    .query(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma);
      const cryptoService = registry.getCryptoService();
      
      const result = await cryptoService.getTrendingProjects(ctx.userId!, input);
      return handleServiceResult(result, 'getTrendingProjects');
    }),

  /**
   * Get project metrics over time
   */
  getProjectMetrics: protectedProcedure
    .input(projectMetricsSchema)
    .query(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma);
      const cryptoService = registry.getCryptoService();
      
      const result = await cryptoService.getProjectMetrics(ctx.userId!, input);
      return handleServiceResult(result, 'getProjectMetrics');
    }),

  /**
   * Get competitive analysis for a project
   */
  getCompetitiveAnalysis: protectedProcedure
    .input(competitiveAnalysisSchema)
    .query(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma);
      const cryptoService = registry.getCryptoService();
      
      const result = await cryptoService.getCompetitiveAnalysis(ctx.userId!, input.projectSlug);
      return handleServiceResult(result, 'getCompetitiveAnalysis');
    }),

  /**
   * Find sector influencers using smart followers
   */
  findSectorInfluencers: protectedProcedure
    .input(sectorInfluencersSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma);
      const cryptoService = registry.getCryptoService();
      
      const result = await cryptoService.findSectorInfluencers(ctx.userId!, input);
      return handleServiceResult(result, 'findSectorInfluencers');
    }),

  /**
   * Get market intelligence for AI context
   */
  getMarketIntelligence: protectedProcedure
    .input(marketIntelligenceSchema)
    .query(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma);
      const cryptoService = registry.getCryptoService();
      
      const result = await cryptoService.getMarketIntelligence(ctx.userId!, input);
      return handleServiceResult(result, 'getMarketIntelligence');
    }),

  /**
   * Clear crypto cache (for testing/debugging)
   */
  clearCache: protectedProcedure
    .input(cacheClearSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma);
      const cryptoService = registry.getCryptoService();
      
      const result = await cryptoService.clearCache(input);
      return handleServiceResult(result, 'clearCache');
    }),

  /**
   * Get comprehensive cache statistics
   */
  getCacheStats: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma);
    const cryptoService = registry.getCryptoService();
    
    const result = await cryptoService.getCacheStats();
    return handleServiceResult(result, 'getCacheStats');
  }),

  /**
   * Cleanup expired cache entries
   */
  cleanupCache: protectedProcedure.mutation(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma);
    const cryptoService = registry.getCryptoService();
    
    const result = await cryptoService.cleanupCache();
    return handleServiceResult(result, 'cleanupCache');
  }),

  /**
   * Get crypto service health and performance metrics
   */
  getServiceHealth: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma);
    const cryptoService = registry.getCryptoService();
    
    // Get cache stats and test connection
    const [cacheResult, connectionResult] = await Promise.all([
      cryptoService.getCacheStats(),
      cryptoService.testConnection(ctx.userId!),
    ]);
    
    const health = {
      healthy: connectionResult.success && cacheResult.success,
      connection: connectionResult.success ? connectionResult.data : null,
      cache: cacheResult.success ? cacheResult.data : null,
      timestamp: new Date().toISOString(),
      alerts: [
        ...(connectionResult.success ? [] : ['Cookie.fun API connection failed']),
        ...(cacheResult.success ? [] : ['Cache statistics unavailable']),
      ],
    };
    
    return { success: true, data: health };
  }),

  /**
   * Get trending sectors summary
   */
  getTrendingSectorsSummary: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma);
    const cryptoService = registry.getCryptoService();
    
    // Get sectors and trending projects
    const [sectorsResult, trendingResult] = await Promise.all([
      cryptoService.getSectors(ctx.userId!),
      cryptoService.getTrendingProjects(ctx.userId!, { timeframe: "_7Days", limit: 20 }),
    ]);
    
    if (!sectorsResult.success || !trendingResult.success) {
      return {
        success: false,
        error: 'Failed to fetch sectors or trending data',
      };
    }
    
    // Group trending projects by sector
    const sectorSummary = new Map();
    const trendingProjects = trendingResult.data.data || [];
    
    for (const project of trendingProjects) {
      const sector = project.sector || 'Unknown';
      if (!sectorSummary.has(sector)) {
        sectorSummary.set(sector, {
          sector,
          projectCount: 0,
          totalMindshare: 0,
          topProjects: [],
        });
      }
      
      const sectorData = sectorSummary.get(sector);
      sectorData.projectCount++;
      sectorData.totalMindshare += project.mindshare || 0;
      
      if (sectorData.topProjects.length < 3) {
        sectorData.topProjects.push({
          name: project.name,
          mindshare: project.mindshare,
          symbol: project.symbol,
        });
      }
    }
    
    // Sort by total mindshare
    const sortedSectors = Array.from(sectorSummary.values())
      .sort((a, b) => b.totalMindshare - a.totalMindshare)
      .slice(0, 10); // Top 10 sectors
    
    return {
      success: true,
      data: {
        sectors: sortedSectors,
        totalSectors: sectorsResult.data.data?.length || 0,
        totalTrendingProjects: trendingProjects.length,
        timestamp: new Date().toISOString(),
      },
    };
  }),
});
