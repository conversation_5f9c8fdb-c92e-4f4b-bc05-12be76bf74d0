/**
 * Mentions Router for BuddyChip tRPC API - Modular Architecture
 *
 * Main router that combines all mention-related operations from focused modules:
 * - Fetch operations (tweet fetching, URL validation)
 * - Sentiment analysis (bullish scoring, AI analysis)
 * - Monitoring (account sync, monitoring status)
 * - Management (CRUD operations, bulk operations)
 * - Response operations (AI response generation)
 */

import { createTRPCRouter } from "../../lib/trpc";
import { fetchOperations } from "./fetch-operations";
import { sentimentAnalysis } from "./sentiment-analysis";
import { monitoring } from "./monitoring";
import { management } from "./management";
import { responses } from "./responses";

export const mentionsRouter = createTRPCRouter({
  // Fetch operations (tweet fetching and mention discovery)
  ...fetchOperations,

  // Management operations (CRUD, bulk operations)
  ...management,

  // Sentiment analysis operations
  sentiment: createTRPCRouter(sentimentAnalysis),

  // Monitoring operations (account sync, monitoring status)
  monitoring: monitoring,

  // Response operations (AI response generation)
  responses: responses,
});