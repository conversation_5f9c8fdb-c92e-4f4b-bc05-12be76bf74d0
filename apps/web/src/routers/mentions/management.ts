/**
 * Management Module
 * 
 * Handles CRUD operations for mentions and accounts including:
 * - Mention lifecycle management
 * - Account management operations
 * - Bulk operations and archiving
 * - Data cleanup and maintenance
 */

import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { protectedProcedure } from "../../lib/trpc";
import { MentionService } from "../../services/mention.service";
import { performanceMonitor } from "../../lib/performance-monitor";
import { createMonitoredProcedure, handleTRPCError as mainHandleTRPCError } from "../../lib/trpc-middleware";
import { 
  commonInputSchemas, 
  handleTRPCError, 
  transformMentionData, 
  transformResponseData,
  generatePerformanceRecommendations 
} from "./shared";

export const management = {
  /**
   * Get latest mentions for dashboard
   */
  getLatest: createMonitoredProcedure("getLatestMentions")
    .input(
      commonInputSchemas.pagination.pick({ limit: true })
    )
    .query(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.getMentions(ctx.userId!, {
          limit: input.limit,
          includeAccount: true,
          includeResponses: false,
          sortBy: "createdAt",
          sortOrder: "desc",
          filters: {
            archived: false,
          },
        });

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to fetch latest mentions",
          });
        }

        return {
          success: true,
          mentions: result.data!.mentions,
        };
      } catch (error) {
        handleTRPCError(error, "fetch latest mentions", {
          userId: ctx.userId,
          limit: input.limit
        });
      }
    }),

  /**
   * Get all mentions with pagination (for reply-guy page)
   */
  getAll: createMonitoredProcedure("getAllMentions")
    .input(
      z.object({
        cursor: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
        accountId: z.string().optional(),
        hasAIResponse: z.boolean().optional(),
        includeResponses: z.boolean().default(false),
        includeAccount: z.boolean().default(true),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        console.log("🔍 Management.getAll: Starting query with input:", input);

        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.getMentions(ctx.userId!, {
          limit: input.limit,
          offset: input.cursor,
          includeAccount: input.includeAccount,
          includeResponses: input.includeResponses,
          sortBy: "createdAt",
          sortOrder: "desc",
          filters: {
            accountId: input.accountId,
            hasAIResponse: input.hasAIResponse,
            archived: false,
          },
        });

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to fetch mentions",
          });
        }

        console.log("✅ Management.getAll: Query completed successfully");

        return {
          success: true,
          mentions: result.data!.mentions,
          nextCursor: result.data!.nextCursor,
          hasNextPage: result.data!.hasNextPage,
          meta: result.data!.meta,
        };
      } catch (error) {
        handleTRPCError(error, "fetch all mentions", {
          userId: ctx.userId,
          ...input
        });
      }
    }),

  /**
   * Get mention by ID with all details
   */
  getById: createMonitoredProcedure("getMentionById")
    .input(commonInputSchemas.mentionId)
    .query(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.getMentionById(ctx.userId!, input.mentionId);

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found") ? "NOT_FOUND" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to fetch mention",
          });
        }

        return {
          success: true,
          mention: result.data,
        };
      } catch (error) {
        handleTRPCError(error, "fetch mention by ID", {
          userId: ctx.userId,
          mentionId: input.mentionId,
        });
      }
    }),

  /**
   * Delete a mention
   */
  delete: createMonitoredProcedure("deleteMention")
    .input(
      z.object({
        mentionId: z.string().cuid("Invalid mention ID"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.deleteMention(ctx.userId!, input.mentionId);

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found") ? "NOT_FOUND" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to delete mention",
          });
        }

        return {
          success: true,
          message: result.message || "Mention deleted successfully",
        };
      } catch (error) {
        handleTRPCError(error, "delete mention", {
          userId: ctx.userId,
          mentionId: input.mentionId,
        });
      }
    }),

  /**
   * Mark mention as having AI response
   */
  markAsReplied: createMonitoredProcedure("markMentionAsReplied")
    .input(commonInputSchemas.mentionId)
    .mutation(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.markMentionAsReplied(ctx.userId!, input.mentionId);

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found") ? "NOT_FOUND" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to mark mention as replied",
          });
        }

        return {
          success: true,
          message: result.message || "Mention marked as replied",
        };
      } catch (error) {
        handleTRPCError(error, "mark as replied", {
          userId: ctx.userId,
          mentionId: input.mentionId,
        });
      }
    }),

  /**
   * Archive a mention
   */
  archive: createMonitoredProcedure("archiveMention")
    .input(commonInputSchemas.mentionId)
    .mutation(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.archiveMention(ctx.userId!, input.mentionId);

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found") ? "NOT_FOUND" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to archive mention",
          });
        }

        return {
          success: true,
          mention: {
            id: input.mentionId,
            ...result.data,
          },
          message: result.message || "Mention archived successfully",
        };
      } catch (error) {
        handleTRPCError(error, "archive mention", {
          userId: ctx.userId,
          mentionId: input.mentionId,
        });
      }
    }),

  /**
   * Unarchive a mention
   */
  unarchive: createMonitoredProcedure("unarchiveMention")
    .input(commonInputSchemas.mentionId)
    .mutation(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.unarchiveMention(ctx.userId!, input.mentionId);

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found") ? "NOT_FOUND" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to unarchive mention",
          });
        }

        return {
          success: true,
          mention: {
            id: input.mentionId,
            ...result.data,
          },
          message: result.message || "Mention unarchived successfully",
        };
      } catch (error) {
        handleTRPCError(error, "unarchive mention", {
          userId: ctx.userId,
          mentionId: input.mentionId,
        });
      }
    }),

  /**
   * Bulk archive multiple mentions
   */
  bulkArchive: createMonitoredProcedure("bulkArchiveMentions")
    .input(
      z.object({
        mentionIds: z
          .array(z.string())
          .min(1, "At least one mention ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.bulkArchiveMentions(ctx.userId!, input.mentionIds);

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found") ? "BAD_REQUEST" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to archive mentions",
          });
        }

        return {
          success: true,
          archivedCount: result.data!.processed,
          message: result.data!.message,
        };
      } catch (error) {
        handleTRPCError(error, "bulk archive mentions", {
          userId: ctx.userId,
          count: input.mentionIds.length,
        });
      }
    }),

  /**
   * Get archived mentions
   */
  getArchived: createMonitoredProcedure("getArchivedMentions")
    .input(
      z.object({
        cursor: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
        accountId: z.string().optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.getMentions(ctx.userId!, {
          limit: input.limit,
          offset: input.cursor,
          includeAccount: true,
          includeResponses: true,
          sortBy: "createdAt",
          sortOrder: "desc",
          filters: {
            accountId: input.accountId,
            archived: true,
          },
        });

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to fetch archived mentions",
          });
        }

        return {
          success: true,
          mentions: result.data!.mentions,
          nextCursor: result.data!.nextCursor,
          hasNextPage: result.data!.hasNextPage,
        };
      } catch (error) {
        handleTRPCError(error, "fetch archived mentions", {
          userId: ctx.userId,
          ...input
        });
      }
    }),

  /**
   * Delete an AI response
   */
  deleteResponse: createMonitoredProcedure("deleteAIResponse")
    .input(
      z.object({
        responseId: z.string().cuid("Invalid response ID"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // First, verify the response exists and belongs to the user
        const response = await ctx.prisma.aIResponse.findFirst({
          where: {
            id: input.responseId,
            userId: ctx.userId!,
          },
          include: {
            mention: {
              select: {
                id: true,
                userId: true,
              },
            },
          },
        });

        if (!response) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "AI response not found or access denied",
          });
        }

        // Verify the mention also belongs to the user (double security check)
        if (response.mention.userId !== ctx.userId) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Access denied",
          });
        }

        // Delete the AI response
        await ctx.prisma.aIResponse.delete({
          where: {
            id: input.responseId,
          },
        });

        return {
          success: true,
          message: "AI response deleted successfully",
        };
      } catch (error) {
        handleTRPCError(error, "delete AI response", {
          userId: ctx.userId,
          responseId: input.responseId,
        });
      }
    }),

  /**
   * Get performance statistics for debugging and monitoring
   */
  getPerformanceStats: createMonitoredProcedure("getPerformanceStats")
    .input(
      z.object({
        minutes: z.number().min(1).max(1440).default(60), // Default to last hour, max 24 hours
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const stats = performanceMonitor.getStats(input.minutes);

        // Log summary to console for debugging
        performanceMonitor.logSummary(input.minutes);

        return {
          success: true,
          stats: {
            timeRange: `${input.minutes} minutes`,
            queryCount: stats.queryCount,
            averageDuration: Math.round(stats.averageDuration * 100) / 100, // Round to 2 decimals
            slowQueriesCount: stats.slowQueries.length,
            topSlowQueries: stats.topSlowQueries.map((q) => ({
              ...q,
              avgDuration: Math.round(q.avgDuration * 100) / 100,
            })),
            recommendations: generatePerformanceRecommendations(stats),
          },
        };
      } catch (error) {
        handleTRPCError(error, "get performance statistics", {
          userId: ctx.userId,
          minutes: input.minutes,
        });
      }
    }),

  /**
   * Bulk delete mentions
   */
  bulkDelete: createMonitoredProcedure("bulkDeleteMentions")
    .input(
      z.object({
        mentionIds: z
          .array(z.string())
          .min(1, "At least one mention ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const results = [];
        const errors = [];

        for (const mentionId of input.mentionIds) {
          try {
            const result = await mentionService.deleteMention(ctx.userId!, mentionId);
            if (result.success) {
              results.push(mentionId);
            } else {
              errors.push(`Failed to delete ${mentionId}: ${result.error}`);
            }
          } catch (error) {
            errors.push(`Error deleting ${mentionId}: ${error instanceof Error ? error.message : "Unknown error"}`);
          }
        }

        return {
          success: errors.length === 0,
          deletedCount: results.length,
          deletedIds: results,
          errors,
          message: errors.length === 0 
            ? `Successfully deleted ${results.length} mentions`
            : `Deleted ${results.length} mentions with ${errors.length} errors`,
        };
      } catch (error) {
        handleTRPCError(error, "bulk delete mentions", {
          userId: ctx.userId,
          count: input.mentionIds.length,
        });
      }
    }),

  /**
   * Get mention statistics
   */
  getStats: createMonitoredProcedure("getMentionStats")
    .input(
      z.object({
        accountId: z.string().optional(),
        days: z.number().min(1).max(365).default(30),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const whereClause: any = {
          userId: ctx.userId!,
        };

        if (input.accountId) {
          whereClause.accountId = input.accountId;
        }

        const dateThreshold = new Date(Date.now() - input.days * 24 * 60 * 60 * 1000);

        // Get various statistics
        const [
          totalMentions,
          recentMentions,
          archivedMentions,
          processedMentions,
          avgBullishScore,
          avgImportanceScore,
        ] = await Promise.all([
          ctx.prisma.mention.count({ where: whereClause }),
          ctx.prisma.mention.count({
            where: {
              ...whereClause,
              createdAt: { gte: dateThreshold },
            },
          }),
          ctx.prisma.mention.count({
            where: {
              ...whereClause,
              archived: true,
            },
          }),
          ctx.prisma.mention.count({
            where: {
              ...whereClause,
              processed: true,
            },
          }),
          ctx.prisma.mention.aggregate({
            where: {
              ...whereClause,
              bullishScore: { not: null },
            },
            _avg: {
              bullishScore: true,
            },
          }),
          ctx.prisma.mention.aggregate({
            where: {
              ...whereClause,
              importanceScore: { not: null },
            },
            _avg: {
              importanceScore: true,
            },
          }),
        ]);

        return {
          success: true,
          stats: {
            totalMentions,
            recentMentions,
            archivedMentions,
            processedMentions,
            unprocessedMentions: totalMentions - processedMentions,
            avgBullishScore: Math.round((avgBullishScore._avg.bullishScore || 0) * 100) / 100,
            avgImportanceScore: Math.round((avgImportanceScore._avg.importanceScore || 0) * 100) / 100,
            processingRate: totalMentions > 0 ? Math.round((processedMentions / totalMentions) * 100) : 0,
            timeRange: `${input.days} days`,
          },
        };
      } catch (error) {
        handleTRPCError(error, "get mention statistics", {
          userId: ctx.userId,
          accountId: input.accountId,
          days: input.days,
        });
      }
    }),
};