/**
 * Sentiment Analysis Module
 * 
 * Handles bullish scoring and sentiment processing including:
 * - Bullish score calculation and updates
 * - Importance score analysis
 * - AI-powered content analysis
 * - Sentiment trend analysis
 */

import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { protectedProcedure } from "../../lib/trpc";
import { MentionService } from "../../services/mention.service";
import { BenjiService } from "../../services/benji.service";
import { FeatureType } from "../../../prisma/generated";
import { createFeatureProcedure, createMonitoredProcedure, handleTRPCError as mainHandleTRPCError } from "../../lib/trpc-middleware";
import { commonInputSchemas, handleTRPCError } from "./shared";

export const sentimentAnalysis = {
  /**
   * Update bullish score manually
   */
  updateBullishScore: createMonitoredProcedure("updateBullishScore")
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
        bullishScore: z.number().min(0).max(100),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.updateBullishScore(
          ctx.userId!,
          input.mentionId,
          input.bullishScore
        );

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found") ? "NOT_FOUND" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to update bullish score",
          });
        }

        return {
          success: true,
          mention: result.data,
          message: result.message || "Bullish score updated successfully",
        };
      } catch (error) {
        handleTRPCError(error, "update bullish score", {
          userId: ctx.userId,
          mentionId: input.mentionId,
          bullishScore: input.bullishScore,
        });
      }
    }),

  /**
   * Update importance score manually
   */
  updateImportanceScore: createMonitoredProcedure("updateImportanceScore")
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
        importanceScore: z.number().min(0).max(100),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const result = await mentionService.updateImportanceScore(
          ctx.userId!,
          input.mentionId,
          input.importanceScore
        );

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found") ? "NOT_FOUND" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to update importance score",
          });
        }

        return {
          success: true,
          mention: result.data,
          message: result.message || "Importance score updated successfully",
        };
      } catch (error) {
        handleTRPCError(error, "update importance score", {
          userId: ctx.userId,
          mentionId: input.mentionId,
          importanceScore: input.importanceScore,
        });
      }
    }),

  /**
   * Analyze content with AI for sentiment and scoring
   */
  analyzeContent: createFeatureProcedure(FeatureType.AI_CALLS, {
    requestedAmount: 1,
    operationName: "analyzeContent",
  })
    .input(
      z.object({
        content: z.string().min(1, "Content is required"),
        authorInfo: z.object({
          name: z.string(),
          handle: z.string(),
          followers: z.number().optional(),
          verified: z.boolean().optional(),
          avatarUrl: z.string().optional(),
        }).optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const benjiService = new BenjiService(ctx.prisma, { 
          info: (msg: string) => console.log(`[BenjiService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[BenjiService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[BenjiService] ${msg}`),
          debug: (msg: string) => console.debug(`[BenjiService] ${msg}`),
        });

        const result = await benjiService.analyzeContent(
          ctx.userId!,
          input.content,
          input.authorInfo
        );

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("limit exceeded") ? "TOO_MANY_REQUESTS" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to analyze content",
          });
        }

        return {
          success: true,
          analysis: result.data,
          message: "Content analysis completed successfully",
        };
      } catch (error) {
        handleTRPCError(error, "analyze content", {
          userId: ctx.userId,
          contentLength: input.content.length,
        });
      }
    }),

  /**
   * Calculate bullish score using built-in algorithm
   */
  calculateBullishScore: createMonitoredProcedure("calculateBullishScore")
    .input(
      z.object({
        content: z.string().min(1, "Content is required"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const bullishScore = mentionService.calculateBullishScore(input.content);
        const keywords = mentionService.extractKeywords(input.content);

        return {
          success: true,
          bullishScore,
          keywords,
          message: "Bullish score calculated successfully",
        };
      } catch (error) {
        handleTRPCError(error, "calculate bullish score", {
          userId: ctx.userId,
          contentLength: input.content.length,
        });
      }
    }),

  /**
   * Extract keywords from content
   */
  extractKeywords: createMonitoredProcedure("extractKeywords")
    .input(
      z.object({
        content: z.string().min(1, "Content is required"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const keywords = mentionService.extractKeywords(input.content);

        return {
          success: true,
          keywords,
          message: "Keywords extracted successfully",
        };
      } catch (error) {
        handleTRPCError(error, "extract keywords", {
          userId: ctx.userId,
          contentLength: input.content.length,
        });
      }
    }),

  /**
   * Get sentiment trends for user's mentions
   */
  getSentimentTrends: createMonitoredProcedure("getSentimentTrends")
    .input(
      z.object({
        accountId: z.string().optional(),
        days: z.number().min(1).max(90).default(7),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const whereClause: any = {
          userId: ctx.userId!,
          archived: false,
          mentionedAt: {
            gte: new Date(Date.now() - input.days * 24 * 60 * 60 * 1000),
          },
        };

        if (input.accountId) {
          whereClause.accountId = input.accountId;
        }

        // Get mentions with sentiment data
        const mentions = await ctx.prisma.mention.findMany({
          where: whereClause,
          select: {
            id: true,
            bullishScore: true,
            importanceScore: true,
            mentionedAt: true,
            keywords: true,
          },
          orderBy: {
            mentionedAt: 'desc',
          },
        });

        // Calculate trends
        const totalMentions = mentions.length;
        const avgBullishScore = mentions.length > 0 
          ? mentions.reduce((sum, m) => sum + (m.bullishScore || 0), 0) / mentions.length
          : 0;
        const avgImportanceScore = mentions.length > 0 
          ? mentions.reduce((sum, m) => sum + (m.importanceScore || 0), 0) / mentions.length
          : 0;

        // Group by day
        const dailyTrends = mentions.reduce((acc, mention) => {
          const date = mention.mentionedAt.toISOString().split('T')[0];
          if (!acc[date]) {
            acc[date] = {
              date,
              count: 0,
              avgBullishScore: 0,
              avgImportanceScore: 0,
              mentions: [],
            };
          }
          acc[date].count++;
          acc[date].mentions.push(mention);
          return acc;
        }, {} as Record<string, any>);

        // Calculate daily averages
        Object.values(dailyTrends).forEach((day: any) => {
          day.avgBullishScore = day.mentions.reduce((sum: number, m: any) => sum + (m.bullishScore || 0), 0) / day.mentions.length;
          day.avgImportanceScore = day.mentions.reduce((sum: number, m: any) => sum + (m.importanceScore || 0), 0) / day.mentions.length;
          delete day.mentions; // Remove raw mentions for cleaner response
        });

        // Extract trending keywords
        const allKeywords = mentions.flatMap(m => m.keywords || []);
        const keywordCounts = allKeywords.reduce((acc, keyword) => {
          acc[keyword] = (acc[keyword] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        const trendingKeywords = Object.entries(keywordCounts)
          .sort(([, a], [, b]) => b - a)
          .slice(0, 10)
          .map(([keyword, count]) => ({ keyword, count }));

        return {
          success: true,
          trends: {
            totalMentions,
            avgBullishScore: Math.round(avgBullishScore * 100) / 100,
            avgImportanceScore: Math.round(avgImportanceScore * 100) / 100,
            dailyTrends: Object.values(dailyTrends),
            trendingKeywords,
          },
          message: "Sentiment trends calculated successfully",
        };
      } catch (error) {
        handleTRPCError(error, "get sentiment trends", {
          userId: ctx.userId,
          accountId: input.accountId,
          days: input.days,
        });
      }
    }),

  /**
   * Bulk update sentiment scores
   */
  bulkUpdateScores: createMonitoredProcedure("bulkUpdateScores")
    .input(
      z.object({
        updates: z.array(
          z.object({
            mentionId: z.string(),
            bullishScore: z.number().min(0).max(100).optional(),
            importanceScore: z.number().min(0).max(100).optional(),
          })
        ).min(1, "At least one update is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const mentionService = new MentionService(ctx.prisma, { 
          info: (msg: string) => console.log(`[MentionService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[MentionService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[MentionService] ${msg}`),
          debug: (msg: string) => console.debug(`[MentionService] ${msg}`),
        });

        const results = [];
        const errors = [];

        for (const update of input.updates) {
          try {
            if (update.bullishScore !== undefined) {
              const result = await mentionService.updateBullishScore(
                ctx.userId!,
                update.mentionId,
                update.bullishScore
              );
              if (!result.success) {
                errors.push(`Failed to update bullish score for ${update.mentionId}: ${result.error}`);
              } else {
                results.push({ mentionId: update.mentionId, type: 'bullish', success: true });
              }
            }

            if (update.importanceScore !== undefined) {
              const result = await mentionService.updateImportanceScore(
                ctx.userId!,
                update.mentionId,
                update.importanceScore
              );
              if (!result.success) {
                errors.push(`Failed to update importance score for ${update.mentionId}: ${result.error}`);
              } else {
                results.push({ mentionId: update.mentionId, type: 'importance', success: true });
              }
            }
          } catch (error) {
            errors.push(`Error updating ${update.mentionId}: ${error instanceof Error ? error.message : "Unknown error"}`);
          }
        }

        return {
          success: errors.length === 0,
          results,
          errors,
          message: errors.length === 0 
            ? `Successfully updated ${results.length} scores`
            : `Completed with ${errors.length} errors`,
        };
      } catch (error) {
        handleTRPCError(error, "bulk update scores", {
          userId: ctx.userId,
          updateCount: input.updates.length,
        });
      }
    }),
};