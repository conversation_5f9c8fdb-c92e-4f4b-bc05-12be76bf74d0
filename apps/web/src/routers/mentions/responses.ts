/**
 * Responses Module
 * 
 * Handles AI response operations including:
 * - AI response generation for mentions
 * - Enhanced response generation with tools
 * - Response management and quality control
 * - Quick reply functionality
 */

import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure } from "../../lib/trpc";
import { BenjiService } from "../../services/benji.service";
import { FeatureType } from "../../../prisma/generated";
import { createFeatureProcedure, createMonitoredProcedure, handleTRPCError as mainHandleTRPCError } from "../../lib/trpc-middleware";
import { commonInputSchemas, handleTRPCError } from "./shared";

export const responses = createTRPCRouter({
  /**
   * Generate basic AI response for a mention
   */
  generate: createFeatureProcedure(FeatureType.AI_CALLS, {
    requestedAmount: 1,
    operationName: "generateMentionResponse",
  })
    .input(commonInputSchemas.mentionId)
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          "🤖 Responses.generate: Starting AI response generation for mention:",
          input.mentionId
        );

        // Verify the mention belongs to the user and get the monitored account info
        const mention = await ctx.prisma.mention.findFirst({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
          include: {
            account: true,
          },
        });

        if (!mention) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Mention not found",
          });
        }

        const benjiService = new BenjiService(ctx.prisma, { 
          info: (msg: string) => console.log(`[BenjiService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[BenjiService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[BenjiService] ${msg}`),
          debug: (msg: string) => console.debug(`[BenjiService] ${msg}`),
        });

        // Generate basic AI response
        const result = await benjiService.generateMentionResponse(ctx.userId!, {
          mentionId: input.mentionId,
          mentionContent: mention.content,
          monitoredAccountInfo: mention.account
            ? {
                name: mention.account.displayName || mention.account.twitterHandle,
                handle: mention.account.twitterHandle,
                avatarUrl: mention.account.avatarUrl || undefined,
              }
            : undefined,
          mentionAuthorInfo: {
            name: mention.authorName,
            handle: mention.authorHandle,
            avatarUrl: mention.authorAvatarUrl || undefined,
          },
          enhancedMode: false,
        });

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("limit exceeded") ? "TOO_MANY_REQUESTS" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to generate AI response",
          });
        }

        // Store the AI response in database
        const aiResponse = await ctx.prisma.aIResponse.create({
          data: {
            mentionId: input.mentionId,
            userId: ctx.userId!,
            content: result.data!.content,
            model: result.data!.model,
            tokensUsed: result.data!.tokensUsed,
            processingTime: result.data!.processingTime,
            confidence: result.data!.quality === "high" ? 90 : result.data!.quality === "medium" ? 75 : 60,
          },
        });

        // Update mention as processed
        await ctx.prisma.mention.update({
          where: { id: input.mentionId },
          data: { processed: true },
        });

        return {
          success: true,
          response: {
            id: aiResponse.id,
            content: result.data!.content,
            model: result.data!.model,
            tokensUsed: result.data!.tokensUsed,
            confidence: aiResponse.confidence,
            quality: result.data!.quality,
          },
          message: "AI response generated successfully",
        };
      } catch (error) {
        mainHandleTRPCError(error, "generate AI response", {
          userId: ctx.userId,
          mentionId: input.mentionId,
        });
      }
    }),

  /**
   * Enhance mention with AI-powered reply generation using tools
   */
  enhance: createFeatureProcedure(FeatureType.AI_CALLS, {
    requestedAmount: 2, // Enhanced responses use more resources
    operationName: "enhanceMentionResponse",
  })
    .input(commonInputSchemas.mentionId)
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          "🔧 Responses.enhance: Starting enhanced reply generation for mention:",
          input.mentionId
        );

        // Verify the mention belongs to the user and get the monitored account info
        const mention = await ctx.prisma.mention.findFirst({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
          include: {
            account: true,
          },
        });

        if (!mention) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Mention not found",
          });
        }

        const benjiService = new BenjiService(ctx.prisma, { 
          info: (msg: string) => console.log(`[BenjiService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[BenjiService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[BenjiService] ${msg}`),
          debug: (msg: string) => console.debug(`[BenjiService] ${msg}`),
        });

        // Generate enhanced AI response with tools
        const result = await benjiService.generateMentionResponse(ctx.userId!, {
          mentionId: input.mentionId,
          mentionContent: mention.content,
          monitoredAccountInfo: mention.account
            ? {
                name: mention.account.displayName || mention.account.twitterHandle,
                handle: mention.account.twitterHandle,
                avatarUrl: mention.account.avatarUrl || undefined,
              }
            : undefined,
          mentionAuthorInfo: {
            name: mention.authorName,
            handle: mention.authorHandle,
            avatarUrl: mention.authorAvatarUrl || undefined,
          },
          enhancedMode: true,
        });

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("limit exceeded") ? "TOO_MANY_REQUESTS" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to enhance mention",
          });
        }

        // Store the enhanced AI response in database
        const aiResponse = await ctx.prisma.aIResponse.create({
          data: {
            mentionId: input.mentionId,
            userId: ctx.userId!,
            content: result.data!.content,
            model: result.data!.model,
            tokensUsed: result.data!.tokensUsed,
            processingTime: result.data!.processingTime,
            confidence: result.data!.quality === "high" ? 95 : result.data!.quality === "medium" ? 85 : 70,
          },
        });

        // Update mention as processed
        await ctx.prisma.mention.update({
          where: { id: input.mentionId },
          data: { processed: true },
        });

        return {
          success: true,
          response: {
            id: aiResponse.id,
            content: result.data!.content,
            model: result.data!.model,
            actualModel: "enhanced",
            tokensUsed: result.data!.tokensUsed,
            confidence: aiResponse.confidence,
            quality: result.data!.quality,
          },
          message: "Enhanced AI response generated successfully",
        };
      } catch (error) {
        mainHandleTRPCError(error, "enhance mention", {
          userId: ctx.userId,
          mentionId: input.mentionId,
        });
      }
    }),

  /**
   * Generate quick reply for any tweet content
   */
  quickReply: createFeatureProcedure(FeatureType.AI_CALLS, {
    requestedAmount: 1,
    operationName: "generateQuickReply",
  })
    .input(
      z.object({
        tweetContent: z.string().min(1, "Tweet content is required"),
        tweetAuthor: z.object({
          name: z.string(),
          handle: z.string(),
        }).optional(),
        responseType: z.enum(["professional", "casual", "supportive", "engaging"]).default("engaging"),
        maxLength: z.number().min(10).max(280).default(280),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          "⚡ Responses.quickReply: Generating quick reply for tweet content:",
          input.tweetContent.substring(0, 100)
        );

        const benjiService = new BenjiService(ctx.prisma, { 
          info: (msg: string) => console.log(`[BenjiService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[BenjiService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[BenjiService] ${msg}`),
          debug: (msg: string) => console.debug(`[BenjiService] ${msg}`),
        });

        const result = await benjiService.generateQuickReply(ctx.userId!, {
          tweetContent: input.tweetContent,
          tweetAuthor: input.tweetAuthor,
          responseType: input.responseType,
          maxLength: input.maxLength,
        });

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("limit exceeded") ? "TOO_MANY_REQUESTS" : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to generate quick reply",
          });
        }

        return {
          success: true,
          response: {
            content: result.data!.content,
            model: result.data!.model,
            tokensUsed: result.data!.tokensUsed,
            quality: result.data!.quality,
            processingTime: result.data!.processingTime,
          },
          message: "Quick reply generated successfully",
        };
      } catch (error) {
        mainHandleTRPCError(error, "generate quick reply", {
          userId: ctx.userId,
          contentLength: input.tweetContent.length,
          responseType: input.responseType,
        });
      }
    }),

  /**
   * Get AI model settings for the user
   */
  getAISettings: createMonitoredProcedure("getAISettings")
    .query(async ({ ctx }) => {
      try {
        console.log(`⚙️ Responses.getAISettings: Getting AI settings for user ${ctx.userId}`);

        const benjiService = new BenjiService(ctx.prisma, { 
          info: (msg: string) => console.log(`[BenjiService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[BenjiService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[BenjiService] ${msg}`),
          debug: (msg: string) => console.debug(`[BenjiService] ${msg}`),
        });

        const result = await benjiService.getUserAISettings(ctx.userId!);

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to get AI settings",
          });
        }

        return {
          success: true,
          settings: result.data,
        };
      } catch (error) {
        mainHandleTRPCError(error, "get AI settings", {
          userId: ctx.userId,
        });
      }
    }),

  /**
   * Update AI model settings for the user
   */
  updateAISettings: createMonitoredProcedure("updateAISettings")
    .input(
      z.object({
        personalityId: z.string().optional(),
        customPrompt: z.string().optional(),
        enhancedMode: z.boolean().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(`⚙️ Responses.updateAISettings: Updating AI settings for user ${ctx.userId}`);

        const benjiService = new BenjiService(ctx.prisma, { 
          info: (msg: string) => console.log(`[BenjiService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[BenjiService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[BenjiService] ${msg}`),
          debug: (msg: string) => console.debug(`[BenjiService] ${msg}`),
        });

        const result = await benjiService.updateUserAISettings(ctx.userId!, input);

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to update AI settings",
          });
        }

        return {
          success: true,
          message: result.message || "AI settings updated successfully",
        };
      } catch (error) {
        mainHandleTRPCError(error, "update AI settings", {
          userId: ctx.userId,
          settings: input,
        });
      }
    }),

  /**
   * Get available AI models for the user's plan
   */
  getAvailableModels: createMonitoredProcedure("getAvailableModels")
    .query(async ({ ctx }) => {
      try {
        console.log(`🤖 Responses.getAvailableModels: Getting available models for user ${ctx.userId}`);

        const benjiService = new BenjiService(ctx.prisma, { 
          info: (msg: string) => console.log(`[BenjiService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[BenjiService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[BenjiService] ${msg}`),
          debug: (msg: string) => console.debug(`[BenjiService] ${msg}`),
        });

        const result = await benjiService.getAvailableModels(ctx.userId!);

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to get available models",
          });
        }

        return {
          success: true,
          models: result.data,
        };
      } catch (error) {
        mainHandleTRPCError(error, "get available models", {
          userId: ctx.userId,
        });
      }
    }),

  /**
   * Get conversation context for the user
   */
  getConversationContext: createMonitoredProcedure("getConversationContext")
    .input(
      z.object({
        conversationId: z.string().optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        console.log(`💬 Responses.getConversationContext: Getting conversation context for user ${ctx.userId}`);

        const benjiService = new BenjiService(ctx.prisma, { 
          info: (msg: string) => console.log(`[BenjiService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[BenjiService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[BenjiService] ${msg}`),
          debug: (msg: string) => console.debug(`[BenjiService] ${msg}`),
        });

        const result = await benjiService.getConversationContext(ctx.userId!, input.conversationId);

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to get conversation context",
          });
        }

        return {
          success: true,
          context: result.data,
        };
      } catch (error) {
        mainHandleTRPCError(error, "get conversation context", {
          userId: ctx.userId,
          conversationId: input.conversationId,
        });
      }
    }),

  /**
   * Clear conversation context for the user
   */
  clearConversationContext: createMonitoredProcedure("clearConversationContext")
    .input(
      z.object({
        conversationId: z.string().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(`🧹 Responses.clearConversationContext: Clearing conversation context for user ${ctx.userId}`);

        const benjiService = new BenjiService(ctx.prisma, { 
          info: (msg: string) => console.log(`[BenjiService] ${msg}`),
          error: (msg: string, error?: Error) => console.error(`[BenjiService] ${msg}`, error),
          warn: (msg: string) => console.warn(`[BenjiService] ${msg}`),
          debug: (msg: string) => console.debug(`[BenjiService] ${msg}`),
        });

        const result = await benjiService.clearConversationContext(ctx.userId!, input.conversationId);

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to clear conversation context",
          });
        }

        return {
          success: true,
          message: result.message || "Conversation context cleared successfully",
        };
      } catch (error) {
        mainHandleTRPCError(error, "clear conversation context", {
          userId: ctx.userId,
          conversationId: input.conversationId,
        });
      }
    }),

  /**
   * Get response quality metrics
   */
  getQualityMetrics: createMonitoredProcedure("getQualityMetrics")
    .input(
      z.object({
        days: z.number().min(1).max(90).default(7),
        mentionId: z.string().optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        console.log(`📊 Responses.getQualityMetrics: Getting quality metrics for user ${ctx.userId}`);

        const whereClause: any = {
          userId: ctx.userId!,
        };

        if (input.mentionId) {
          whereClause.mentionId = input.mentionId;
        } else {
          whereClause.createdAt = {
            gte: new Date(Date.now() - input.days * 24 * 60 * 60 * 1000),
          };
        }

        const responses = await ctx.prisma.aIResponse.findMany({
          where: whereClause,
          select: {
            id: true,
            model: true,
            confidence: true,
            tokensUsed: true,
            processingTime: true,
            createdAt: true,
            used: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

        // Calculate metrics
        const totalResponses = responses.length;
        const usedResponses = responses.filter(r => r.used).length;
        const avgConfidence = responses.length > 0 
          ? responses.reduce((sum, r) => sum + (r.confidence || 0), 0) / responses.length
          : 0;
        const avgTokens = responses.length > 0 
          ? responses.reduce((sum, r) => sum + r.tokensUsed, 0) / responses.length
          : 0;
        const avgProcessingTime = responses.length > 0 
          ? responses.reduce((sum, r) => sum + r.processingTime, 0) / responses.length
          : 0;

        // Group by model
        const modelMetrics = responses.reduce((acc, response) => {
          const model = response.model || 'unknown';
          if (!acc[model]) {
            acc[model] = {
              model,
              count: 0,
              avgConfidence: 0,
              avgTokens: 0,
              avgProcessingTime: 0,
              used: 0,
            };
          }
          acc[model].count++;
          acc[model].avgConfidence += response.confidence || 0;
          acc[model].avgTokens += response.tokensUsed;
          acc[model].avgProcessingTime += response.processingTime;
          if (response.used) {
            acc[model].used++;
          }
          return acc;
        }, {} as Record<string, any>);

        // Calculate averages for each model
        Object.values(modelMetrics).forEach((metric: any) => {
          metric.avgConfidence = Math.round((metric.avgConfidence / metric.count) * 100) / 100;
          metric.avgTokens = Math.round(metric.avgTokens / metric.count);
          metric.avgProcessingTime = Math.round(metric.avgProcessingTime / metric.count);
          metric.usageRate = Math.round((metric.used / metric.count) * 100);
        });

        return {
          success: true,
          metrics: {
            totalResponses,
            usedResponses,
            usageRate: totalResponses > 0 ? Math.round((usedResponses / totalResponses) * 100) : 0,
            avgConfidence: Math.round(avgConfidence * 100) / 100,
            avgTokens: Math.round(avgTokens),
            avgProcessingTime: Math.round(avgProcessingTime),
            modelBreakdown: Object.values(modelMetrics),
            timeRange: input.mentionId ? 'single mention' : `${input.days} days`,
          },
        };
      } catch (error) {
        mainHandleTRPCError(error, "get quality metrics", {
          userId: ctx.userId,
          days: input.days,
          mentionId: input.mentionId,
        });
      }
    }),
});