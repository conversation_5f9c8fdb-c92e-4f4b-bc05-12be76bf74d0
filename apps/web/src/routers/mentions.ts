/**
 * Mentions Router for BuddyChip tRPC API - Modular Architecture
 *
 * Main router that combines all mention-related operations from focused modules:
 * - Query operations (getLatest, getAll, getById)
 * - Mutation operations (create, update, delete)
 * - Sync operations (account sync, bulk operations)
 * - Response operations (AI response generation)
 */

export { mentionsRouter } from "./mentions";