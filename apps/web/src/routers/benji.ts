/**
 * Benji tRPC Router
 *
 * Handles AI agent operations and interactions
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { FeatureType } from "../../prisma/generated/index.js";
import { BenjiAgent, getBenjiForUser } from "../lib/benji";
import mem0Service from "../lib/mem0-service";
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";
import { createFeatureProcedure, createMonitoredProcedure, handleTRPCError } from "../lib/trpc-middleware";

export const benjiRouter = createTRPCRouter({
  /**
   * Generate AI response for a mention
   */
  generateMentionResponse: createFeatureProcedure(FeatureType.AI_CALLS, {
    requestedAmount: 1,
    operationName: "generateMentionResponse",
  })
    .input(
      z.object({
        mentionId: z.string(),
        mentionContent: z.string(),
        authorInfo: z
          .object({
            name: z.string(),
            handle: z.string(),
            avatarUrl: z.string().optional(),
          })
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        console.log("🚀 Benji Router: Starting mention response generation");
        console.log("📝 Benji Router: Input:", {
          mentionId: input.mentionId,
          contentLength: input.mentionContent.length,
          authorHandle: input.authorInfo?.handle,
        });

        // Get user's Benji agent
        const benji = await getBenjiForUser(ctx.userId);

        console.log("🤖 Benji Router: Agent created, generating response...");

        // Generate response
        const result = await benji.generateMentionResponse(
          input.mentionContent,
          {
            mentionId: input.mentionId,
            mentionContent: input.mentionContent,
            authorInfo: input.authorInfo,
          }
        );

        console.log("✅ Benji Router: Response generation started");

        // Convert streaming result to response
        console.log("📡 Benji Router: Processing text stream...");
        let responseText = "";
        for await (const chunk of result.textStream) {
          responseText += chunk;
        }

        console.log("✅ Benji Router: Response generated:", {
          length: responseText.length,
          preview: responseText.substring(0, 100) + "...",
        });

        // Store the response in database if we have mentionId
        if (input.mentionId && responseText) {
          try {
            await ctx.prisma.aIResponse.create({
              data: {
                mentionId: input.mentionId,
                userId: ctx.userId,
                content: responseText,
                model: "gemini-2.5-flash",
                tokensUsed: (await result.usage)?.totalTokens || 0,
                processingTime: Date.now(),
              },
            });
          } catch (dbError) {
            console.error("Failed to store AI response:", dbError);
            // Don't fail the request if storage fails
          }
        }

        return {
          response: responseText,
          model: "gemini-2.5-flash",
          usage: await result.usage,
        };
      } catch (error) {
        handleTRPCError(error, "generateMentionResponse", { 
          mentionId: input.mentionId,
          authorHandle: input.authorInfo?.handle,
        });
      }
    }),

  /**
   * Generate quick reply for any tweet
   */
  generateQuickReply: createFeatureProcedure(FeatureType.AI_CALLS, {
    requestedAmount: 1,
    operationName: "generateQuickReply",
  })
    .input(
      z.object({
        tweetUrl: z.string().url(),
        tweetContent: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // Get user's Benji agent
        const benji = await getBenjiForUser(ctx.userId);

        // Generate response
        const result = await benji.generateQuickReply(input.tweetContent);

        // Convert streaming result to response
        let responseText = "";
        for await (const chunk of result.textStream) {
          responseText += chunk;
        }

        return {
          response: responseText,
          model: "gemini-2.5-flash",
          usage: await result.usage,
        };
      } catch (error) {
        handleTRPCError(error, "generateQuickReply", { 
          tweetUrl: input.tweetUrl,
        });
      }
    }),

  /**
   * Calculate bullish score for a tweet
   */
  calculateBullishScore: createMonitoredProcedure("calculateBullishScore")
    .input(
      z.object({
        content: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const benji = await getBenjiForUser(ctx.userId);
        const score = await benji.calculateBullishScore(input.content);

        return { score };
      } catch (error) {
        handleTRPCError(error, "calculateBullishScore", { 
          contentLength: input.content.length,
        });
      }
    }),

  /**
   * Get user's AI usage statistics
   */
  getUsageStats: createMonitoredProcedure("getUsageStats").query(async ({ ctx }) => {
    const currentPeriod = new Date().toISOString().slice(0, 7); // YYYY-MM format

    const stats = await ctx.prisma.usageLog.groupBy({
      by: ["feature"],
      where: {
        userId: ctx.userId,
        billingPeriod: currentPeriod,
      },
      _sum: {
        amount: true,
      },
    });

    const usage = stats.reduce(
      (acc, stat) => {
        acc[stat.feature] = stat._sum.amount || 0;
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      currentPeriod,
      usage,
    };
  }),

  /**
   * Get available models and capabilities
   */
  getCapabilities: createMonitoredProcedure("getCapabilities").query(async ({ ctx }) => {
    const user = await ctx.prisma.user.findUnique({
      where: { id: ctx.userId },
      include: { plan: true },
    });

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User not found",
      });
    }

    return {
      plan: user.plan.name,
      models: [
        {
          id: "gemini-2.5-flash",
          name: "Gemini 2.5 Flash",
          available: true,
          description: "Fast and efficient for general tasks",
        },
        {
          id: "gemini-2.5-pro",
          name: "Gemini 2.5 Pro",
          available: user.plan.name !== "reply-guy",
          description: "Advanced reasoning for complex tasks",
        },
        {
          id: "openai-o3",
          name: "OpenAI o1-mini",
          available: user.plan.name === "team-plan",
          description: "Latest reasoning model",
        },
      ],
      tools: [
        {
          name: "Web Search",
          description: "Real-time web search via xAI",
          enabled: true,
        },
        {
          name: "Knowledge Search",
          description: "Semantic search via Exa",
          enabled: true,
        },
        {
          name: "Image Generation",
          description: "AI image generation via DALL-E",
          enabled: user.plan.name !== "reply-guy",
        },
      ],
    };
  }),

  /**
   * Generate enhanced mention response with crypto market intelligence
   */
  generateEnhancedMentionResponse: createFeatureProcedure(FeatureType.AI_CALLS, {
    requestedAmount: 2,
    operationName: "generateEnhancedMentionResponse",
  })
    .input(
      z.object({
        mentionId: z.string(),
        mentionContent: z.string(),
        authorInfo: z
          .object({
            name: z.string(),
            handle: z.string(),
            avatarUrl: z.string().optional(),
          })
          .optional(),
        monitoredAccountInfo: z
          .object({
            name: z.string(),
            handle: z.string(),
            avatarUrl: z.string().optional(),
          })
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        console.log(
          "🚀 Benji Router: Starting enhanced mention response with market intelligence"
        );
        console.log("📝 Benji Router: Input:", {
          mentionId: input.mentionId,
          contentLength: input.mentionContent.length,
          authorHandle: input.authorInfo?.handle,
          monitoredAccount: input.monitoredAccountInfo?.handle,
        });

        // Get user's Benji agent
        const benji = await getBenjiForUser(ctx.userId);

        console.log(
          "🤖 Benji Router: Agent created, generating enhanced response with market intelligence..."
        );

        // Generate enhanced response with market intelligence
        const result =
          await benji.generateEnhancedMentionResponseWithIntelligence(
            input.mentionContent,
            {
              mentionId: input.mentionId,
              mentionContent: input.mentionContent,
              authorInfo: input.authorInfo,
              monitoredAccountInfo: input.monitoredAccountInfo,
            }
          );

        console.log("✅ Benji Router: Enhanced response generation started");

        // Convert streaming result to response
        console.log("📡 Benji Router: Processing enhanced text stream...");
        let responseText = "";
        for await (const chunk of result.textStream) {
          responseText += chunk;
        }

        console.log("✅ Benji Router: Enhanced response generated:", {
          length: responseText.length,
          mentionId: input.mentionId,
        });

        return {
          success: true,
          response: responseText,
          mentionId: input.mentionId,
          enhanced: true,
          model: "openaiO3",
          features: ["market_intelligence", "openai_o3"],
        };
      } catch (error) {
        handleTRPCError(error, "generateEnhancedMentionResponse", {
          mentionId: input.mentionId,
          authorHandle: input.authorInfo?.handle,
          monitoredAccount: input.monitoredAccountInfo?.handle,
        });
      }
    }),

  /**
   * Search user's conversation memories
   */
  searchMemories: createMonitoredProcedure("searchMemories")
    .input(
      z.object({
        query: z.string(),
        limit: z.number().min(1).max(10).default(5),
        memoryType: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        console.log(
          "🔍 Benji Router: Searching memories for user:",
          ctx.userId
        );

        const memories = await mem0Service.searchMemories(ctx.userId, {
          query: input.query,
          limit: input.limit,
          memoryType: input.memoryType,
        });

        return {
          memories: memories.map((memory) => ({
            id: memory.id,
            content: memory.content,
            similarity: memory.similarity,
            memoryType: memory.memoryType,
            createdAt: memory.createdAt,
          })),
          count: memories.length,
        };
      } catch (error) {
        console.error("Memory search error:", error);

        // Handle rate limiting errors specifically
        if (error instanceof Error && error.message.includes("rate limit")) {
          throw new TRPCError({
            code: "TOO_MANY_REQUESTS",
            message: error.message,
          });
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to search memories",
        });
      }
    }),

  /**
   * Get all user's memories (for dashboard/management)
   */
  getUserMemories: createMonitoredProcedure("getUserMemories")
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(50),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        console.log("📋 Benji Router: Getting memories for user:", ctx.userId);

        const memories = await mem0Service.getUserMemories(
          ctx.userId,
          input.limit
        );

        return {
          memories: memories.map((memory) => ({
            id: memory.id,
            content: memory.content,
            memoryType: memory.memoryType,
            createdAt: memory.createdAt,
            metadata: memory.metadata,
          })),
          count: memories.length,
        };
      } catch (error) {
        console.error("Get user memories error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to retrieve memories",
        });
      }
    }),

  /**
   * Delete specific memories (for privacy/GDPR compliance)
   */
  deleteMemories: createMonitoredProcedure("deleteMemories")
    .input(
      z.object({
        memoryIds: z.array(z.string()).min(1).max(10),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        console.log(
          "🗑️ Benji Router: Deleting memories for user:",
          ctx.userId,
          input.memoryIds
        );

        await mem0Service.deleteMemories(ctx.userId, input.memoryIds);

        return {
          success: true,
          deletedCount: input.memoryIds.length,
        };
      } catch (error) {
        console.error("Delete memories error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete memories",
        });
      }
    }),

  /**
   * Get memory service health status
   */
  getMemoryHealth: createMonitoredProcedure("getMemoryHealth").query(async ({ ctx }) => {
    try {
      const health = await mem0Service.healthCheck();

      return {
        status: health.status,
        error: health.error,
        userId: ctx.userId,
      };
    } catch (error) {
      console.error("Memory health check error:", error);
      return {
        status: "unhealthy" as const,
        error: error instanceof Error ? error.message : "Unknown error",
        userId: ctx.userId,
      };
    }
  }),

  /**
   * Get memory-enhanced context for a query (for testing/debugging)
   */
  getMemoryContext: createMonitoredProcedure("getMemoryContext")
    .input(
      z.object({
        query: z.string(),
        limit: z.number().min(1).max(5).default(3),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        console.log(
          "🧠 Benji Router: Getting memory context for user:",
          ctx.userId
        );

        const memoryContext = await mem0Service.getUserMemoryContext(
          ctx.userId,
          input.query,
          input.limit
        );

        return {
          context: memoryContext,
          hasMemories: memoryContext.length > 0,
          query: input.query,
        };
      } catch (error) {
        console.error("Get memory context error:", error);

        // Handle rate limiting errors specifically
        if (error instanceof Error && error.message.includes("rate limit")) {
          throw new TRPCError({
            code: "TOO_MANY_REQUESTS",
            message: error.message,
          });
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get memory context",
        });
      }
    }),

  /**
   * Get available tools for the current user
   */
  getAvailableTools: createMonitoredProcedure("getAvailableTools")
    .query(async ({ ctx }) => {
      try {
        const benji = await getBenjiForUser(ctx.userId);
        const tools = benji.getAvailableTools();
        const toolHealth = benji.getToolHealth();
        
        return {
          tools: Object.keys(tools),
          toolHealth,
          userPlan: benji.getConfig().userPlan,
        };
      } catch (error) {
        handleTRPCError(error, "getAvailableTools", { userId: ctx.userId });
      }
    }),

  /**
   * Get tool usage statistics for the current user
   */
  getToolUsageStats: createMonitoredProcedure("getToolUsageStats")
    .query(async ({ ctx }) => {
      try {
        const benji = await getBenjiForUser(ctx.userId);
        const stats = benji.getToolUsageStats();
        
        return {
          usage: stats,
          totalTools: stats.length,
        };
      } catch (error) {
        handleTRPCError(error, "getToolUsageStats", { userId: ctx.userId });
      }
    }),

  /**
   * Get tool recommendations based on content
   */
  getToolRecommendations: createMonitoredProcedure("getToolRecommendations")
    .input(
      z.object({
        contentType: z.enum(["crypto", "tech", "general"]).default("general"),
        needsResearch: z.boolean().default(false),
        needsVisuals: z.boolean().default(false),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const benji = await getBenjiForUser(ctx.userId);
        const recommendations = benji.getToolRecommendations({
          contentType: input.contentType,
          needsResearch: input.needsResearch,
          needsVisuals: input.needsVisuals,
        });
        
        return {
          recommendations,
          context: input,
        };
      } catch (error) {
        handleTRPCError(error, "getToolRecommendations", { userId: ctx.userId });
      }
    }),

  /**
   * Reset tool usage for a new conversation
   */
  resetToolUsage: createMonitoredProcedure("resetToolUsage")
    .mutation(async ({ ctx }) => {
      try {
        const benji = await getBenjiForUser(ctx.userId);
        benji.resetToolUsage();
        
        return {
          success: true,
          message: "Tool usage reset successfully",
        };
      } catch (error) {
        handleTRPCError(error, "resetToolUsage", { userId: ctx.userId });
      }
    }),
});
