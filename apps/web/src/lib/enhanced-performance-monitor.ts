/**
 * Enhanced Performance Monitoring System
 * 
 * Comprehensive performance monitoring that includes:
 * - Server-side performance metrics
 * - Client-side Core Web Vitals
 * - User analytics and behavior tracking
 * - Real-time alerts and notifications
 * - Business intelligence metrics
 */

import * as Sentry from "@sentry/nextjs";
import { performanceMonitor } from "./performance-monitor";

// Core Web Vitals and client-side metrics
export interface WebVitalsMetric {
  name: "CLS" | "FID" | "FCP" | "LCP" | "TTFB" | "INP";
  value: number;
  rating: "good" | "needs-improvement" | "poor";
  delta: number;
  id: string;
  navigationType: "navigate" | "reload" | "back-forward" | "prerender";
  url: string;
  timestamp: number;
}

// User behavior analytics
export interface UserAnalyticsEvent {
  type: "page_view" | "feature_usage" | "user_action" | "error" | "conversion";
  userId?: string;
  sessionId: string;
  page?: string;
  feature?: string;
  action?: string;
  properties?: Record<string, any>;
  timestamp: number;
  duration?: number;
}

// Business intelligence metrics
export interface BusinessMetric {
  type: "user_engagement" | "feature_adoption" | "performance_kpi" | "revenue_metric";
  name: string;
  value: number;
  unit: string;
  dimensions: Record<string, string>;
  timestamp: number;
}

// Performance alert
export interface PerformanceAlert {
  id: string;
  type: "performance" | "error" | "business" | "security";
  severity: "low" | "medium" | "high" | "critical";
  title: string;
  message: string;
  metric?: string;
  threshold?: number;
  currentValue?: number;
  timestamp: number;
  resolved?: boolean;
}

/**
 * Enhanced Performance Monitor with comprehensive tracking
 */
class EnhancedPerformanceMonitor {
  private static instance: EnhancedPerformanceMonitor;
  private webVitalsMetrics: WebVitalsMetric[] = [];
  private analyticsEvents: UserAnalyticsEvent[] = [];
  private businessMetrics: BusinessMetric[] = [];
  private alerts: PerformanceAlert[] = [];
  private sessionId: string;

  // Performance thresholds
  private readonly thresholds = {
    // Core Web Vitals thresholds (Google standards)
    CLS: { good: 0.1, poor: 0.25 },
    FID: { good: 100, poor: 300 },
    FCP: { good: 1800, poor: 3000 },
    LCP: { good: 2500, poor: 4000 },
    TTFB: { good: 800, poor: 1800 },
    INP: { good: 200, poor: 500 },
    
    // Server-side thresholds
    queryTime: { good: 100, poor: 1000 },
    apiResponse: { good: 200, poor: 1000 },
    pageLoad: { good: 2000, poor: 5000 },
  };

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeClientSideMonitoring();
  }

  static getInstance(): EnhancedPerformanceMonitor {
    if (!EnhancedPerformanceMonitor.instance) {
      EnhancedPerformanceMonitor.instance = new EnhancedPerformanceMonitor();
    }
    return EnhancedPerformanceMonitor.instance;
  }

  /**
   * Initialize client-side performance monitoring
   */
  private initializeClientSideMonitoring() {
    if (typeof window === "undefined") return;

    // Monitor Core Web Vitals
    this.initializeWebVitals();
    
    // Monitor page navigation
    this.initializeNavigationMonitoring();
    
    // Monitor user interactions
    this.initializeUserInteractionMonitoring();
    
    // Monitor errors
    this.initializeErrorMonitoring();
  }

  /**
   * Initialize Web Vitals monitoring
   */
  private initializeWebVitals() {
    if (typeof window === "undefined") return;

    // Use web-vitals library if available, otherwise implement basic monitoring
    const reportWebVital = (metric: any) => {
      const webVital: WebVitalsMetric = {
        name: metric.name,
        value: metric.value,
        rating: this.getRating(metric.name, metric.value),
        delta: metric.delta,
        id: metric.id,
        navigationType: (performance.getEntriesByType("navigation")[0] as any)?.type || "navigate",
        url: window.location.href,
        timestamp: Date.now(),
      };

      this.recordWebVital(webVital);
    };

    // Try to use web-vitals library
    try {
      import("web-vitals").then(({ onCLS, onINP, onFCP, onLCP, onTTFB }) => {
        onCLS(reportWebVital);
        onINP(reportWebVital);
        onFCP(reportWebVital);
        onLCP(reportWebVital);
        onTTFB(reportWebVital);
      }).catch(() => {
        // Fallback to basic monitoring
        this.initializeBasicWebVitals();
      });
    } catch {
      this.initializeBasicWebVitals();
    }
  }

  /**
   * Basic Web Vitals monitoring fallback
   */
  private initializeBasicWebVitals() {
    if (typeof window === "undefined") return;

    // Monitor LCP
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      this.recordWebVital({
        name: "LCP",
        value: lastEntry.startTime,
        rating: this.getRating("LCP", lastEntry.startTime),
        delta: lastEntry.startTime,
        id: this.generateId(),
        navigationType: "navigate",
        url: window.location.href,
        timestamp: Date.now(),
      });
    }).observe({ entryTypes: ["largest-contentful-paint"] });

    // Monitor FCP
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const firstEntry = entries[0];
      
      this.recordWebVital({
        name: "FCP",
        value: firstEntry.startTime,
        rating: this.getRating("FCP", firstEntry.startTime),
        delta: firstEntry.startTime,
        id: this.generateId(),
        navigationType: "navigate",
        url: window.location.href,
        timestamp: Date.now(),
      });
    }).observe({ entryTypes: ["paint"] });
  }

  /**
   * Initialize navigation monitoring
   */
  private initializeNavigationMonitoring() {
    if (typeof window === "undefined") return;

    // Monitor page loads
    window.addEventListener("load", () => {
      const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
      
      this.trackUserAnalytics({
        type: "page_view",
        sessionId: this.sessionId,
        page: window.location.pathname,
        properties: {
          loadTime: navigation.loadEventEnd - navigation.fetchStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
          firstByte: navigation.responseStart - navigation.fetchStart,
        },
        timestamp: Date.now(),
        duration: navigation.loadEventEnd - navigation.fetchStart,
      });
    });

    // Monitor route changes (for SPAs)
    let currentPath = window.location.pathname;
    const checkRouteChange = () => {
      if (window.location.pathname !== currentPath) {
        currentPath = window.location.pathname;
        
        this.trackUserAnalytics({
          type: "page_view",
          sessionId: this.sessionId,
          page: currentPath,
          properties: {
            navigationType: "spa_navigation",
          },
          timestamp: Date.now(),
        });
      }
    };

    // Check for route changes periodically
    setInterval(checkRouteChange, 1000);
  }

  /**
   * Initialize user interaction monitoring
   */
  private initializeUserInteractionMonitoring() {
    if (typeof window === "undefined") return;

    // Monitor clicks
    document.addEventListener("click", (event) => {
      const target = event.target as HTMLElement;
      const elementInfo = this.getElementInfo(target);
      
      this.trackUserAnalytics({
        type: "user_action",
        sessionId: this.sessionId,
        action: "click",
        page: window.location.pathname,
        properties: {
          element: elementInfo,
          timestamp: event.timeStamp,
        },
        timestamp: Date.now(),
      });
    });

    // Monitor form submissions
    document.addEventListener("submit", (event) => {
      const form = event.target as HTMLFormElement;
      
      this.trackUserAnalytics({
        type: "user_action",
        sessionId: this.sessionId,
        action: "form_submit",
        page: window.location.pathname,
        properties: {
          formId: form.id,
          formAction: form.action,
        },
        timestamp: Date.now(),
      });
    });
  }

  /**
   * Initialize error monitoring
   */
  private initializeErrorMonitoring() {
    if (typeof window === "undefined") return;

    // Monitor JavaScript errors
    window.addEventListener("error", (event) => {
      this.trackUserAnalytics({
        type: "error",
        sessionId: this.sessionId,
        page: window.location.pathname,
        properties: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
        },
        timestamp: Date.now(),
      });

      // Also send to Sentry
      Sentry.captureException(event.error);
    });

    // Monitor unhandled promise rejections
    window.addEventListener("unhandledrejection", (event) => {
      this.trackUserAnalytics({
        type: "error",
        sessionId: this.sessionId,
        page: window.location.pathname,
        properties: {
          reason: event.reason,
          type: "unhandled_promise_rejection",
        },
        timestamp: Date.now(),
      });

      // Also send to Sentry
      Sentry.captureException(event.reason);
    });
  }

  /**
   * Record Web Vital metric
   */
  recordWebVital(metric: WebVitalsMetric) {
    this.webVitalsMetrics.push(metric);
    
    // Keep only recent metrics
    if (this.webVitalsMetrics.length > 1000) {
      this.webVitalsMetrics = this.webVitalsMetrics.slice(-1000);
    }

    // Check for performance alerts
    this.checkWebVitalThresholds(metric);

    // Send to external analytics if configured
    this.sendToAnalytics("web_vital", metric);

    console.log(`📊 Web Vital: ${metric.name} = ${metric.value}ms (${metric.rating})`);
  }

  /**
   * Track user analytics event
   */
  trackUserAnalytics(event: UserAnalyticsEvent) {
    this.analyticsEvents.push(event);
    
    // Keep only recent events
    if (this.analyticsEvents.length > 5000) {
      this.analyticsEvents = this.analyticsEvents.slice(-5000);
    }

    // Send to external analytics
    this.sendToAnalytics("user_event", event);

    if (event.type === "error") {
      console.error("🚨 User Analytics Error:", event);
    } else {
      console.log("📈 User Analytics:", event.type, event.action || event.page);
    }
  }

  /**
   * Record business metric
   */
  recordBusinessMetric(metric: BusinessMetric) {
    this.businessMetrics.push(metric);
    
    // Keep only recent metrics
    if (this.businessMetrics.length > 1000) {
      this.businessMetrics = this.businessMetrics.slice(-1000);
    }

    // Send to external analytics
    this.sendToAnalytics("business_metric", metric);

    console.log(`💼 Business Metric: ${metric.name} = ${metric.value} ${metric.unit}`);
  }

  /**
   * Create performance alert
   */
  createAlert(alert: Omit<PerformanceAlert, "id" | "timestamp">) {
    const fullAlert: PerformanceAlert = {
      ...alert,
      id: this.generateId(),
      timestamp: Date.now(),
    };

    this.alerts.push(fullAlert);

    // Send critical alerts immediately
    if (alert.severity === "critical") {
      this.sendCriticalAlert(fullAlert);
    }

    console.warn(`🚨 Performance Alert [${alert.severity}]: ${alert.title}`);
    return fullAlert;
  }

  /**
   * Get performance rating for Web Vital
   */
  private getRating(name: string, value: number): "good" | "needs-improvement" | "poor" {
    const threshold = this.thresholds[name as keyof typeof this.thresholds];
    if (!threshold) return "good";

    if (value <= threshold.good) return "good";
    if (value <= threshold.poor) return "needs-improvement";
    return "poor";
  }

  /**
   * Check Web Vital thresholds and create alerts
   */
  private checkWebVitalThresholds(metric: WebVitalsMetric) {
    if (metric.rating === "poor") {
      this.createAlert({
        type: "performance",
        severity: "medium",
        title: `Poor ${metric.name} Performance`,
        message: `${metric.name} is ${metric.value}ms, which is considered poor performance`,
        metric: metric.name,
        currentValue: metric.value,
        threshold: this.thresholds[metric.name]?.poor,
      });
    }
  }

  /**
   * Send data to external analytics services
   */
  private sendToAnalytics(type: string, data: any) {
    // Send to Sentry for performance monitoring
    if (type === "web_vital") {
      Sentry.addBreadcrumb({
        category: "web-vital",
        message: `${data.name}: ${data.value}ms (${data.rating})`,
        level: data.rating === "poor" ? "warning" : "info",
        data,
      });
    }

    // Send to other analytics services (Google Analytics, Mixpanel, etc.)
    // This would be implemented based on your analytics stack
  }

  /**
   * Send critical alert notifications
   */
  private sendCriticalAlert(alert: PerformanceAlert) {
    // Send to Sentry
    Sentry.captureMessage(`Critical Performance Alert: ${alert.title}`, "error");

    // Send to other notification services (Slack, email, etc.)
    // This would be implemented based on your notification preferences
  }

  /**
   * Utility functions
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private getElementInfo(element: HTMLElement) {
    return {
      tagName: element.tagName,
      id: element.id,
      className: element.className,
      textContent: element.textContent?.slice(0, 100),
    };
  }

  /**
   * Get comprehensive performance report
   */
  getPerformanceReport(timeWindow: number = 3600000) { // 1 hour default
    const cutoff = Date.now() - timeWindow;
    
    const recentWebVitals = this.webVitalsMetrics.filter(m => m.timestamp > cutoff);
    const recentAnalytics = this.analyticsEvents.filter(e => e.timestamp > cutoff);
    const recentBusinessMetrics = this.businessMetrics.filter(m => m.timestamp > cutoff);
    const recentAlerts = this.alerts.filter(a => a.timestamp > cutoff);

    return {
      summary: {
        timeWindow: timeWindow / 1000 / 60, // minutes
        webVitalsCount: recentWebVitals.length,
        analyticsEventsCount: recentAnalytics.length,
        businessMetricsCount: recentBusinessMetrics.length,
        alertsCount: recentAlerts.length,
        criticalAlertsCount: recentAlerts.filter(a => a.severity === "critical").length,
      },
      webVitals: this.aggregateWebVitals(recentWebVitals),
      analytics: this.aggregateAnalytics(recentAnalytics),
      businessMetrics: recentBusinessMetrics,
      alerts: recentAlerts,
      serverMetrics: performanceMonitor.getStats(timeWindow / 1000 / 60),
    };
  }

  private aggregateWebVitals(metrics: WebVitalsMetric[]) {
    const byName = metrics.reduce((acc, metric) => {
      if (!acc[metric.name]) acc[metric.name] = [];
      acc[metric.name].push(metric);
      return acc;
    }, {} as Record<string, WebVitalsMetric[]>);

    return Object.entries(byName).map(([name, values]) => ({
      name,
      count: values.length,
      average: values.reduce((sum, v) => sum + v.value, 0) / values.length,
      ratings: {
        good: values.filter(v => v.rating === "good").length,
        needsImprovement: values.filter(v => v.rating === "needs-improvement").length,
        poor: values.filter(v => v.rating === "poor").length,
      },
    }));
  }

  private aggregateAnalytics(events: UserAnalyticsEvent[]) {
    const byType = events.reduce((acc, event) => {
      if (!acc[event.type]) acc[event.type] = 0;
      acc[event.type]++;
      return acc;
    }, {} as Record<string, number>);

    const pageViews = events.filter(e => e.type === "page_view");
    const errors = events.filter(e => e.type === "error");

    return {
      eventsByType: byType,
      uniquePages: new Set(pageViews.map(e => e.page)).size,
      errorRate: errors.length / events.length,
      topPages: this.getTopPages(pageViews),
    };
  }

  private getTopPages(pageViews: UserAnalyticsEvent[]) {
    const pageCounts = pageViews.reduce((acc, event) => {
      const page = event.page || "unknown";
      acc[page] = (acc[page] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(pageCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([page, count]) => ({ page, count }));
  }
}

// Export singleton instance
export const enhancedPerformanceMonitor = EnhancedPerformanceMonitor.getInstance();
