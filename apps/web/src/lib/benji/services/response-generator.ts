/**
 * Response Generator Service - Core AI response generation
 * 
 * Handles generating responses for mentions, quick replies, posts,
 * and enhanced responses with tool support.
 */

import { streamText } from "ai";
import { getModel, type ModelName } from "../../ai-providers";
import { ToolService } from "./tool-service";
import { ToolRegistry, ToolUsageTracker } from "./tools";
import type { BenjiContext, BenjiConfig } from "../types";

export class ResponseGeneratorService {
  private config: BenjiConfig;
  private toolService: ToolService;
  private toolRegistry: ToolRegistry;
  private usageTracker: ToolUsageTracker;

  constructor(config: BenjiConfig) {
    this.config = config;
    this.toolService = new ToolService(config);
    this.toolRegistry = ToolRegistry.getInstance();
    this.usageTracker = new ToolUsageTracker();
  }

  /**
   * Generate streaming response with default model
   */
  async streamResponse(
    messages: Array<{ role: "system" | "user" | "assistant"; content: string }>,
    context: BenjiContext = {}
  ) {
    return this.streamResponseWithModel(messages, context, this.config.model!);
  }

  /**
   * Generate streaming response with specific model override
   */
  async streamResponseWithModel(
    messages: Array<{ role: "system" | "user" | "assistant"; content: string }>,
    context: BenjiContext = {},
    modelName: ModelName
  ) {
    console.log("🔄 ResponseGenerator: Starting stream with config:", {
      model: modelName,
      enableTools: this.config.enableTools,
      maxTokens: this.config.maxTokens,
      userId: this.config.userId,
    });

    try {
      const model = getModel(modelName);
      console.log("✅ ResponseGenerator: Model loaded successfully:", modelName);

      // Configure tools using the new tool service
      const tools = this.toolService.getToolConfiguration();

      console.log(
        "🛠️ ResponseGenerator: Tools configured:",
        this.config.enableTools ? "Enabled" : "Disabled"
      );

      // Log tool availability
      if (tools && this.config.userId) {
        const toolHealth = this.toolService.getToolHealth();
        console.log("🔍 ResponseGenerator: Tool health:", toolHealth);
      }

      // Special configuration for o3 model
      const isO3Model = modelName === "openaiO3";
      const streamConfig = {
        model,
        messages,
        tools: isO3Model ? undefined : tools, // o3 might not support tools initially
        maxTokens: isO3Model
          ? Math.min(this.config.maxTokens || 4000, 2000)
          : this.config.maxTokens, // o3 might have lower limits
        temperature: isO3Model ? 0.7 : this.config.temperature, // o3 might work better with specific temperature
        maxSteps: isO3Model ? 1 : this.config.maxSteps, // o3 might not support multi-step
      };

      console.log("🔧 ResponseGenerator: streamText configuration:", {
        modelName,
        isO3Model,
        maxTokens: streamConfig.maxTokens,
        temperature: streamConfig.temperature,
        maxSteps: streamConfig.maxSteps,
        toolsEnabled: !!streamConfig.tools,
        messagesCount: messages.length,
      });

      const result = streamText(streamConfig);

      console.log("🚀 ResponseGenerator: streamText call initiated successfully");
      return result;
    } catch (error) {
      console.error("❌ ResponseGenerator: streamResponse error:", error);
      console.error("🔍 ResponseGenerator: Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
        config: this.config,
        requestedModel: modelName,
      });
      throw new Error(
        `AI generation failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Try generation with o3 model, fallback to Gemini Pro
   */
  async tryO3WithFallback(
    messages: Array<{ role: "system" | "user" | "assistant"; content: string }>,
    context: BenjiContext = {}
  ) {
    try {
      console.log("🎯 ResponseGenerator: Attempting OpenAI o3 via OpenRouter...");
      return await this.streamResponseWithModel(messages, context, "openaiO3");
    } catch (error) {
      console.warn(
        "⚠️ ResponseGenerator: o3 model failed, falling back to Gemini Pro:",
        error
      );
      console.warn("🔍 ResponseGenerator: o3 error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        name: error instanceof Error ? error.name : "Unknown",
        stack: error instanceof Error ? error.stack?.split("\n")[0] : undefined,
      });
      console.log("🔄 ResponseGenerator: Using Gemini Pro as enhanced fallback...");
      return await this.streamResponseWithModel(
        messages,
        context,
        "gemini25Pro"
      );
    }
  }

  /**
   * Calculate optimal token limit based on content and model
   */
  getOptimalTokenLimit(modelName: ModelName, baseContent: string): number {
    const contentLength = baseContent.length;
    const baseLimit = this.config.maxTokens || 4000;

    // Adjust based on model capabilities
    if (modelName === "openaiO3") {
      return Math.min(baseLimit, 2000); // o3 might have stricter limits
    }

    // Adjust based on content length
    if (contentLength < 100) {
      return Math.min(baseLimit, 500); // Short content = short response
    } else if (contentLength < 500) {
      return Math.min(baseLimit, 1000); // Medium content
    }

    return baseLimit; // Full limit for long content
  }

  /**
   * Validate and prepare messages for generation
   */
  validateMessages(
    messages: Array<{ role: "system" | "user" | "assistant"; content: string }>
  ): void {
    if (!messages || messages.length === 0) {
      throw new Error("No messages provided for generation");
    }

    // Ensure system message exists
    const hasSystemMessage = messages.some((msg) => msg.role === "system");
    if (!hasSystemMessage) {
      throw new Error("System message is required");
    }

    // Ensure user message exists
    const hasUserMessage = messages.some((msg) => msg.role === "user");
    if (!hasUserMessage) {
      throw new Error("User message is required");
    }
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<BenjiConfig>): void {
    this.config = { ...this.config, ...updates };
    this.toolService.updateConfig(this.config);
  }

  /**
   * Get tool usage statistics
   */
  getToolUsageStats() {
    return this.toolService.getToolUsageStats();
  }

  /**
   * Reset tool usage for new conversation
   */
  resetToolUsage(): void {
    this.toolService.resetConversationUsage();
  }

  /**
   * Get available tools for current configuration
   */
  getAvailableTools() {
    return this.toolService.getAvailableTools();
  }

  /**
   * Check if a specific tool can be used
   */
  canUseTool(toolName: string): boolean {
    return this.toolService.canUseTool(toolName);
  }
}