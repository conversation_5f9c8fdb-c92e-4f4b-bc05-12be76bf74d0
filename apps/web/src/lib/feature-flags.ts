/**
 * Feature Flags System for BuddyChip Refactoring
 * 
 * Provides a comprehensive feature flag system for gradual rollout
 * of refactored components and new features.
 */

import type { SubscriptionPlan } from "../../prisma/generated/index.js";
import React from "react";

/**
 * Available feature flags for refactoring
 */
export enum RefactorFlag {
  // Utility Library
  USE_NEW_UTILITY_LIBRARY = "USE_NEW_UTILITY_LIBRARY",
  
  // Component System
  USE_UNIFIED_COMPONENTS = "USE_UNIFIED_COMPONENTS",
  USE_NEW_BUTTON_SYSTEM = "USE_NEW_BUTTON_SYSTEM",
  USE_STANDARDIZED_MODALS = "USE_STANDARDIZED_MODALS",
  USE_UNIFIED_FORMS = "USE_UNIFIED_FORMS",
  
  // tRPC Architecture
  USE_NEW_TRPC_MIDDLEWARE = "USE_NEW_TRPC_MIDDLEWARE",
  USE_CONSOLIDATED_ROUTERS = "USE_CONSOLIDATED_ROUTERS",
  USE_COMMON_SCHEMAS = "USE_COMMON_SCHEMAS",
  
  // Database Optimization
  USE_OPTIMIZED_QUERIES = "USE_OPTIMIZED_QUERIES",
  USE_BATCH_OPERATIONS = "USE_BATCH_OPERATIONS",
  USE_QUERY_CACHE = "USE_QUERY_CACHE",
  
  // Module Decomposition
  USE_MODULAR_MENTIONS = "USE_MODULAR_MENTIONS",
  USE_MODULAR_TELEGRAM = "USE_MODULAR_TELEGRAM",
  USE_MODULAR_COOKIE_CLIENT = "USE_MODULAR_COOKIE_CLIENT",
  
  // Benji Agent
  USE_NEW_BENJI_AGENT = "USE_NEW_BENJI_AGENT",
  USE_BENJI_SERVICES = "USE_BENJI_SERVICES",
  
  // Development & Monitoring
  ENABLE_REFACTOR_LOGGING = "ENABLE_REFACTOR_LOGGING",
  ENABLE_PERFORMANCE_MONITORING = "ENABLE_PERFORMANCE_MONITORING",
  ENABLE_QUALITY_CHECKS = "ENABLE_QUALITY_CHECKS",
  ENABLE_MIGRATION_TRACKING = "ENABLE_MIGRATION_TRACKING",
}

/**
 * Feature flag configuration
 */
interface FeatureFlagConfig {
  enabled: boolean;
  rolloutPercentage?: number;
  userGroups?: string[];
  subscriptionPlans?: SubscriptionPlan[];
  startDate?: Date;
  endDate?: Date;
  dependencies?: RefactorFlag[];
  description?: string;
}

/**
 * Default feature flag configurations
 */
const DEFAULT_FLAG_CONFIGS: Record<RefactorFlag, FeatureFlagConfig> = {
  // Utility Library - Safe to enable
  [RefactorFlag.USE_NEW_UTILITY_LIBRARY]: {
    enabled: true,
    description: "Use new comprehensive utility library",
  },
  
  // Component System - Gradual rollout
  [RefactorFlag.USE_UNIFIED_COMPONENTS]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_NEW_UTILITY_LIBRARY],
    description: "Use unified component system",
  },
  [RefactorFlag.USE_NEW_BUTTON_SYSTEM]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_UNIFIED_COMPONENTS],
    description: "Use new unified button system",
  },
  [RefactorFlag.USE_STANDARDIZED_MODALS]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_UNIFIED_COMPONENTS],
    description: "Use standardized modal components",
  },
  [RefactorFlag.USE_UNIFIED_FORMS]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_UNIFIED_COMPONENTS],
    description: "Use unified form components",
  },
  
  // tRPC Architecture - Careful rollout
  [RefactorFlag.USE_NEW_TRPC_MIDDLEWARE]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_NEW_UTILITY_LIBRARY],
    description: "Use new tRPC middleware system",
  },
  [RefactorFlag.USE_CONSOLIDATED_ROUTERS]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_NEW_TRPC_MIDDLEWARE],
    description: "Use consolidated tRPC routers",
  },
  [RefactorFlag.USE_COMMON_SCHEMAS]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_NEW_TRPC_MIDDLEWARE],
    description: "Use common Zod schemas",
  },
  
  // Database Optimization - Performance critical
  [RefactorFlag.USE_OPTIMIZED_QUERIES]: {
    enabled: false,
    rolloutPercentage: 0,
    description: "Use optimized database queries",
  },
  [RefactorFlag.USE_BATCH_OPERATIONS]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_OPTIMIZED_QUERIES],
    description: "Use batch database operations",
  },
  [RefactorFlag.USE_QUERY_CACHE]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_OPTIMIZED_QUERIES],
    description: "Use query result caching",
  },
  
  // Module Decomposition - High risk
  [RefactorFlag.USE_MODULAR_MENTIONS]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_NEW_TRPC_MIDDLEWARE],
    description: "Use modular mentions router",
  },
  [RefactorFlag.USE_MODULAR_TELEGRAM]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_NEW_UTILITY_LIBRARY],
    description: "Use modular Telegram bot architecture",
  },
  [RefactorFlag.USE_MODULAR_COOKIE_CLIENT]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_NEW_UTILITY_LIBRARY],
    description: "Use modular Cookie.fun client",
  },
  
  // Benji Agent - Highest risk
  [RefactorFlag.USE_NEW_BENJI_AGENT]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_BENJI_SERVICES],
    description: "Use new modular Benji agent",
  },
  [RefactorFlag.USE_BENJI_SERVICES]: {
    enabled: false,
    rolloutPercentage: 0,
    dependencies: [RefactorFlag.USE_NEW_UTILITY_LIBRARY],
    description: "Use Benji service architecture",
  },
  
  // Development & Monitoring - Safe for development
  [RefactorFlag.ENABLE_REFACTOR_LOGGING]: {
    enabled: process.env.NODE_ENV === "development",
    description: "Enable detailed refactoring logs",
  },
  [RefactorFlag.ENABLE_PERFORMANCE_MONITORING]: {
    enabled: false,
    description: "Enable performance monitoring for refactored components",
  },
  [RefactorFlag.ENABLE_QUALITY_CHECKS]: {
    enabled: process.env.NODE_ENV === "development",
    description: "Enable runtime quality checks",
  },
  [RefactorFlag.ENABLE_MIGRATION_TRACKING]: {
    enabled: process.env.NODE_ENV === "development",
    description: "Track migration progress and metrics",
  },
};

/**
 * Feature flag evaluation context
 */
interface FeatureFlagContext {
  userId?: string;
  userPlan?: SubscriptionPlan;
  userGroups?: string[];
  timestamp?: Date;
}

/**
 * Feature flag service
 */
class FeatureFlagService {
  private configs: Record<RefactorFlag, FeatureFlagConfig>;
  private overrides: Map<string, boolean> = new Map();
  
  constructor(configs: Record<RefactorFlag, FeatureFlagConfig> = DEFAULT_FLAG_CONFIGS) {
    this.configs = { ...configs };
    this.loadEnvironmentOverrides();
  }
  
  /**
   * Check if a feature flag is enabled for the given context
   */
  isEnabled(flag: RefactorFlag, context: FeatureFlagContext = {}): boolean {
    // Check for environment override first
    const envOverride = this.getEnvironmentOverride(flag);
    if (envOverride !== undefined) {
      return envOverride;
    }
    
    // Check for manual override
    const manualOverride = this.overrides.get(flag);
    if (manualOverride !== undefined) {
      return manualOverride;
    }
    
    const config = this.configs[flag];
    if (!config) {
      console.warn(`Unknown feature flag: ${flag}`);
      return false;
    }
    
    // Check if flag is globally disabled
    if (!config.enabled) {
      return false;
    }
    
    // Check dependencies
    if (config.dependencies) {
      for (const dependency of config.dependencies) {
        if (!this.isEnabled(dependency, context)) {
          return false;
        }
      }
    }
    
    // Check date range
    const now = context.timestamp || new Date();
    if (config.startDate && now < config.startDate) {
      return false;
    }
    if (config.endDate && now > config.endDate) {
      return false;
    }
    
    // Check subscription plan
    if (config.subscriptionPlans && context.userPlan) {
      if (!config.subscriptionPlans.includes(context.userPlan)) {
        return false;
      }
    }
    
    // Check user groups
    if (config.userGroups && context.userGroups) {
      const hasRequiredGroup = config.userGroups.some(group => 
        context.userGroups!.includes(group)
      );
      if (!hasRequiredGroup) {
        return false;
      }
    }
    
    // Check rollout percentage
    if (config.rolloutPercentage !== undefined) {
      if (config.rolloutPercentage === 0) {
        return false;
      }
      if (config.rolloutPercentage === 100) {
        return true;
      }
      
      // Use user ID for consistent rollout
      if (context.userId) {
        const hash = this.hashUserId(context.userId);
        return hash < config.rolloutPercentage;
      }
      
      // Fallback to random for anonymous users
      return Math.random() * 100 < config.rolloutPercentage;
    }
    
    return true;
  }
  
  /**
   * Get all enabled flags for a context
   */
  getEnabledFlags(context: FeatureFlagContext = {}): RefactorFlag[] {
    return Object.values(RefactorFlag).filter(flag => 
      this.isEnabled(flag, context)
    );
  }
  
  /**
   * Override a feature flag (for testing/debugging)
   */
  override(flag: RefactorFlag, enabled: boolean): void {
    this.overrides.set(flag, enabled);
    
    if (this.isLoggingEnabled()) {
      console.log(`🎛️ Feature flag override: ${flag} = ${enabled}`);
    }
  }
  
  /**
   * Clear all overrides
   */
  clearOverrides(): void {
    this.overrides.clear();
  }
  
  /**
   * Update flag configuration
   */
  updateConfig(flag: RefactorFlag, config: Partial<FeatureFlagConfig>): void {
    this.configs[flag] = { ...this.configs[flag], ...config };
    
    if (this.isLoggingEnabled()) {
      console.log(`🎛️ Feature flag config updated: ${flag}`, config);
    }
  }
  
  /**
   * Get flag configuration
   */
  getConfig(flag: RefactorFlag): FeatureFlagConfig | undefined {
    return this.configs[flag];
  }
  
  /**
   * Get all flag configurations
   */
  getAllConfigs(): Record<RefactorFlag, FeatureFlagConfig> {
    return { ...this.configs };
  }
  
  /**
   * Load environment variable overrides
   */
  private loadEnvironmentOverrides(): void {
    for (const flag of Object.values(RefactorFlag)) {
      const envValue = process.env[flag];
      if (envValue !== undefined) {
        const enabled = envValue === "true" || envValue === "1";
        this.configs[flag] = { ...this.configs[flag], enabled };
      }
    }
  }
  
  /**
   * Get environment override for a flag
   */
  private getEnvironmentOverride(flag: RefactorFlag): boolean | undefined {
    const envValue = process.env[flag];
    if (envValue === undefined) {
      return undefined;
    }
    return envValue === "true" || envValue === "1";
  }
  
  /**
   * Hash user ID for consistent rollout
   */
  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash) % 100;
  }
  
  /**
   * Check if logging is enabled
   */
  private isLoggingEnabled(): boolean {
    return this.isEnabled(RefactorFlag.ENABLE_REFACTOR_LOGGING);
  }
}

// Global feature flag service instance
export const featureFlags = new FeatureFlagService();

/**
 * Convenience function to check if a flag is enabled
 */
export function isFeatureEnabled(
  flag: RefactorFlag, 
  context: FeatureFlagContext = {}
): boolean {
  return featureFlags.isEnabled(flag, context);
}

/**
 * React hook for feature flags (to be used in components)
 */
export function useFeatureFlag(
  flag: RefactorFlag,
  context: FeatureFlagContext = {}
): boolean {
  // In a real React implementation, this would use useState and useEffect
  // to re-render when flags change
  return featureFlags.isEnabled(flag, context);
}

/**
 * Higher-order component for feature flag gating
 */
export function withFeatureFlag<P extends object>(
  flag: RefactorFlag,
  Component: React.ComponentType<P>,
  FallbackComponent?: React.ComponentType<P>
) {
  return function FeatureFlaggedComponent(props: P) {
    const isEnabled = useFeatureFlag(flag);
    
    if (isEnabled) {
      return React.createElement(Component, props);
    }
    
    if (FallbackComponent) {
      return React.createElement(FallbackComponent, props);
    }
    
    return null;
  };
}

console.log("🎛️ Feature flags system loaded successfully");
