/**
 * Telegram Input Validation and Sanitization
 *
 * Comprehensive validation and sanitization utilities for Telegram bot inputs
 */

import DOMPurify from "isomorphic-dompurify";
import { z } from "zod";

// Telegram constraints and limits
export const TELEGRAM_LIMITS = {
  MESSAGE_MAX_LENGTH: 4096,
  CALLBACK_DATA_MAX_LENGTH: 64,
  USERNAME_MAX_LENGTH: 15,
  USERNAME_MIN_LENGTH: 1,
  COMMAND_MAX_LENGTH: 64,
  BOT_TOKEN_LENGTH: 46, // Standard Telegram bot token length
  CHAT_ID_MIN: -1000000000000, // Supergroup/channel IDs can be very negative
  CHAT_ID_MAX: 2147483647, // Max 32-bit signed integer
} as const;

// Regular expressions for validation
export const TELEGRAM_REGEX = {
  USERNAME: /^[a-zA-Z0-9_]+$/,
  BOT_TOKEN: /^[0-9]{8,10}:[a-zA-Z0-9_-]{35}$/,
  TWITTER_URL: /^https?:\/\/(twitter\.com|x\.com)\/[a-zA-Z0-9_]+\/status\/\d+/,
  COMMAND: /^\/[a-zA-Z0-9_]+$/,
  MENTION: /@[a-zA-Z0-9_]+/g,
  HASHTAG: /#[a-zA-Z0-9_]+/g,
  URL: /https?:\/\/[^\s]+/g,
} as const;

// Malicious patterns to detect
const MALICIOUS_PATTERNS = [
  /javascript:/i,
  /data:/i,
  /vbscript:/i,
  /onload=/i,
  /onerror=/i,
  /onclick=/i,
  /<script/i,
  /<iframe/i,
  /<object/i,
  /<embed/i,
  /expression\(/i,
  /url\(/i,
  /@import/i,
  /eval\(/i,
  /Function\(/i,
];

// Validation schemas
export const TelegramMessageSchema = z.object({
  text: z
    .string()
    .min(1, "Message cannot be empty")
    .max(
      TELEGRAM_LIMITS.MESSAGE_MAX_LENGTH,
      `Message too long (max ${TELEGRAM_LIMITS.MESSAGE_MAX_LENGTH} characters)`
    )
    .refine(
      (text) => !MALICIOUS_PATTERNS.some((pattern) => pattern.test(text)),
      "Message contains potentially malicious content"
    ),
  chatId: z
    .number()
    .int("Chat ID must be an integer")
    .min(TELEGRAM_LIMITS.CHAT_ID_MIN, "Invalid chat ID")
    .max(TELEGRAM_LIMITS.CHAT_ID_MAX, "Invalid chat ID"),
  messageId: z
    .number()
    .int("Message ID must be an integer")
    .positive("Message ID must be positive"),
});

export const TelegramUserSchema = z.object({
  id: z
    .number()
    .int("User ID must be an integer")
    .positive("User ID must be positive"),
  username: z
    .string()
    .min(TELEGRAM_LIMITS.USERNAME_MIN_LENGTH, "Username too short")
    .max(TELEGRAM_LIMITS.USERNAME_MAX_LENGTH, "Username too long")
    .regex(TELEGRAM_REGEX.USERNAME, "Invalid username format")
    .optional(),
  firstName: z
    .string()
    .min(1, "First name cannot be empty")
    .max(64, "First name too long")
    .optional(),
  lastName: z.string().max(64, "Last name too long").optional(),
  languageCode: z
    .string()
    .length(2, "Invalid language code")
    .regex(/^[a-z]{2}$/, "Language code must be lowercase letters")
    .optional(),
  isBot: z.boolean().optional(),
});

export const TelegramCallbackQuerySchema = z.object({
  id: z
    .string()
    .min(1, "Callback query ID cannot be empty")
    .max(32, "Callback query ID too long"),
  data: z
    .string()
    .max(
      TELEGRAM_LIMITS.CALLBACK_DATA_MAX_LENGTH,
      `Callback data too long (max ${TELEGRAM_LIMITS.CALLBACK_DATA_MAX_LENGTH} characters)`
    )
    .refine(
      (data) => !MALICIOUS_PATTERNS.some((pattern) => pattern.test(data)),
      "Callback data contains potentially malicious content"
    )
    .optional(),
  from: TelegramUserSchema,
});

export const TelegramCommandSchema = z.object({
  command: z
    .string()
    .min(2, "Command too short") // At least /x
    .max(TELEGRAM_LIMITS.COMMAND_MAX_LENGTH, "Command too long")
    .regex(TELEGRAM_REGEX.COMMAND, "Invalid command format")
    .transform((cmd) => cmd.toLowerCase()), // Normalize to lowercase
  args: z
    .array(z.string().max(256, "Command argument too long"))
    .max(10, "Too many command arguments")
    .default([]),
});

/**
 * Sanitize text content for safe processing
 */
export function sanitizeText(text: string): string {
  if (!text || typeof text !== "string") {
    return "";
  }

  // Remove potential HTML/script tags
  let sanitized = DOMPurify.sanitize(text, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
  }) as string;

  // Remove zero-width characters and other invisible characters
  sanitized = sanitized.replace(/[\u200B-\u200D\uFEFF]/g, "");

  // Normalize line endings
  sanitized = sanitized.replace(/\r\n/g, "\n").replace(/\r/g, "\n");

  // Trim excessive whitespace
  sanitized = sanitized.trim();

  // Limit consecutive newlines
  sanitized = sanitized.replace(/\n{3,}/g, "\n\n");

  return sanitized;
}

/**
 * Sanitize callback data for safe processing
 */
export function sanitizeCallbackData(data: string): string {
  if (!data || typeof data !== "string") {
    return "";
  }

  // Remove any potentially dangerous characters
  return data
    .replace(/[<>'"&]/g, "") // Remove HTML special characters
    .replace(/[\x00-\x1F\x7F]/g, "") // Remove control characters
    .trim()
    .substring(0, TELEGRAM_LIMITS.CALLBACK_DATA_MAX_LENGTH);
}

/**
 * Validate and sanitize Twitter URL
 */
export function validateTwitterUrl(url: string): {
  isValid: boolean;
  sanitizedUrl?: string;
  error?: string;
} {
  try {
    if (!url || typeof url !== "string") {
      return { isValid: false, error: "URL is required" };
    }

    // Basic URL validation
    const urlObj = new URL(url);

    // Check if it's a Twitter/X URL
    if (
      !["twitter.com", "x.com", "www.twitter.com", "www.x.com"].includes(
        urlObj.hostname
      )
    ) {
      return { isValid: false, error: "URL must be from Twitter or X" };
    }

    // Check if it's a status URL
    if (!TELEGRAM_REGEX.TWITTER_URL.test(url)) {
      return { isValid: false, error: "URL must be a valid tweet URL" };
    }

    // Check for malicious patterns
    if (MALICIOUS_PATTERNS.some((pattern) => pattern.test(url))) {
      return {
        isValid: false,
        error: "URL contains potentially malicious content",
      };
    }

    // Sanitize and normalize
    const sanitizedUrl = urlObj.toString();

    return { isValid: true, sanitizedUrl };
  } catch (error) {
    return { isValid: false, error: "Invalid URL format" };
  }
}

/**
 * Validate Telegram username
 */
export function validateTelegramUsername(username: string): {
  isValid: boolean;
  sanitizedUsername?: string;
  error?: string;
} {
  if (!username || typeof username !== "string") {
    return { isValid: false, error: "Username is required" };
  }

  // Remove @ symbol if present
  const cleanUsername = username.replace(/^@/, "");

  // Validate length
  if (
    cleanUsername.length < TELEGRAM_LIMITS.USERNAME_MIN_LENGTH ||
    cleanUsername.length > TELEGRAM_LIMITS.USERNAME_MAX_LENGTH
  ) {
    return {
      isValid: false,
      error: `Username must be ${TELEGRAM_LIMITS.USERNAME_MIN_LENGTH}-${TELEGRAM_LIMITS.USERNAME_MAX_LENGTH} characters`,
    };
  }

  // Validate format
  if (!TELEGRAM_REGEX.USERNAME.test(cleanUsername)) {
    return {
      isValid: false,
      error: "Username can only contain letters, numbers, and underscores",
    };
  }

  return { isValid: true, sanitizedUsername: cleanUsername };
}

/**
 * Parse and validate Telegram command
 */
export function parseCommand(text: string): {
  isValid: boolean;
  command?: string;
  args?: string[];
  error?: string;
} {
  if (!text || typeof text !== "string" || !text.startsWith("/")) {
    return { isValid: false, error: "Not a valid command" };
  }

  try {
    const parts = text.trim().split(/\s+/);
    const command = parts[0];
    const args = parts.slice(1);

    const result = TelegramCommandSchema.safeParse({ command, args });

    if (!result.success) {
      return {
        isValid: false,
        error: result.error.errors[0]?.message || "Invalid command format",
      };
    }

    return {
      isValid: true,
      command: result.data.command,
      args: result.data.args,
    };
  } catch (error) {
    return { isValid: false, error: "Failed to parse command" };
  }
}

/**
 * Extract and validate mentions from text
 */
export function extractMentions(text: string): string[] {
  if (!text || typeof text !== "string") {
    return [];
  }

  const mentions = text.match(TELEGRAM_REGEX.MENTION) || [];
  return mentions
    .map((mention) => mention.substring(1)) // Remove @
    .filter((username) => validateTelegramUsername(username).isValid)
    .slice(0, 10); // Limit to 10 mentions max
}

/**
 * Extract and validate URLs from text
 */
export function extractUrls(text: string): string[] {
  if (!text || typeof text !== "string") {
    return [];
  }

  const urls = text.match(TELEGRAM_REGEX.URL) || [];
  return urls
    .filter((url) => {
      try {
        new URL(url);
        return !MALICIOUS_PATTERNS.some((pattern) => pattern.test(url));
      } catch {
        return false;
      }
    })
    .slice(0, 5); // Limit to 5 URLs max
}

/**
 * Validate message content and metadata
 */
export function validateMessage(message: any): {
  isValid: boolean;
  sanitizedMessage?: any;
  errors?: string[];
} {
  try {
    const errors: string[] = [];

    // Validate basic structure
    if (!message || typeof message !== "object") {
      return { isValid: false, errors: ["Invalid message structure"] };
    }

    // Sanitize text if present
    let sanitizedText = "";
    if (message.text) {
      sanitizedText = sanitizeText(message.text);
      if (sanitizedText.length === 0 && message.text.length > 0) {
        errors.push("Message text contains only invalid characters");
      }
    }

    // Validate with schema
    const result = TelegramMessageSchema.safeParse({
      text: sanitizedText,
      chatId: message.chat?.id,
      messageId: message.message_id,
    });

    if (!result.success) {
      errors.push(...result.error.errors.map((e) => e.message));
    }

    // Validate user if present
    if (message.from) {
      const userResult = TelegramUserSchema.safeParse(message.from);
      if (!userResult.success) {
        errors.push(...userResult.error.errors.map((e) => `User ${e.message}`));
      }
    }

    if (errors.length > 0) {
      return { isValid: false, errors };
    }

    return {
      isValid: true,
      sanitizedMessage: {
        ...message,
        text: sanitizedText,
      },
    };
  } catch (error) {
    return { isValid: false, errors: ["Failed to validate message"] };
  }
}

/**
 * Rate limiting helper for input validation
 */
export function shouldRejectBasedOnContent(text: string): {
  shouldReject: boolean;
  reason?: string;
} {
  if (!text || typeof text !== "string") {
    return { shouldReject: false };
  }

  // Check for spam indicators
  if (text.length > 2000 && text.split("\n").length < 5) {
    return {
      shouldReject: true,
      reason: "Potential spam: very long single-line message",
    };
  }

  // Check for excessive repetition
  const words = text.toLowerCase().split(/\s+/);
  const uniqueWords = new Set(words);
  if (words.length > 50 && uniqueWords.size / words.length < 0.3) {
    return {
      shouldReject: true,
      reason: "Potential spam: excessive word repetition",
    };
  }

  // Check for too many URLs
  const urls = extractUrls(text);
  if (urls.length > 3) {
    return { shouldReject: true, reason: "Too many URLs in message" };
  }

  // Check for too many mentions
  const mentions = extractMentions(text);
  if (mentions.length > 5) {
    return { shouldReject: true, reason: "Too many mentions in message" };
  }

  return { shouldReject: false };
}

/**
 * Security check for potentially dangerous content
 */
export function hasSecurityRisk(content: string): {
  hasRisk: boolean;
  risks?: string[];
} {
  if (!content || typeof content !== "string") {
    return { hasRisk: false };
  }

  const risks: string[] = [];

  // Check for malicious patterns
  MALICIOUS_PATTERNS.forEach((pattern, index) => {
    if (pattern.test(content)) {
      risks.push(`Malicious pattern detected: ${pattern.source}`);
    }
  });

  // Check for potential injection attempts
  if (/[<>'"&]/.test(content)) {
    risks.push("HTML/script injection attempt detected");
  }

  // Check for SQL injection patterns (exclude Telegram commands)
  if (
    !content.startsWith('/') && // Don't flag Telegram commands
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)|(-{2})|(\|\|)/i.test(
      content
    )
  ) {
    risks.push("Potential SQL injection attempt");
  }

  // Check for command injection
  if (/[;&|`$(){}[\]]/.test(content)) {
    risks.push("Potential command injection attempt");
  }

  return {
    hasRisk: risks.length > 0,
    risks: risks.length > 0 ? risks : undefined,
  };
}
