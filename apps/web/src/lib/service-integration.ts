/**
 * Service Integration Utilities
 * 
 * Provides utilities for integrating services with tRPC routers,
 * including performance monitoring, error handling, and caching.
 */

import { TRPCError } from '@trpc/server';
import type { PrismaClient } from '../../prisma/generated';
import { getServiceRegistry } from '../services';
import type { ServiceResult } from '../services/interfaces';

/**
 * Service integration configuration
 */
interface ServiceIntegrationConfig {
  enablePerformanceMonitoring?: boolean;
  enableCaching?: boolean;
  enableErrorReporting?: boolean;
  timeout?: number;
  retryAttempts?: number;
}

/**
 * Default configuration for service integration
 */
const DEFAULT_CONFIG: ServiceIntegrationConfig = {
  enablePerformanceMonitoring: true,
  enableCaching: true,
  enableErrorReporting: true,
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
};

/**
 * Service operation metadata
 */
interface ServiceOperation {
  serviceName: string;
  methodName: string;
  userId?: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  success?: boolean;
  error?: string;
  cached?: boolean;
}

/**
 * Service integration class for managing service operations
 */
export class ServiceIntegration {
  private readonly config: ServiceIntegrationConfig;
  private readonly operations: Map<string, ServiceOperation[]> = new Map();
  private readonly cache: Map<string, { data: any; timestamp: Date; ttl: number }> = new Map();

  constructor(config: Partial<ServiceIntegrationConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    // Cleanup cache periodically
    setInterval(() => this.cleanupCache(), 5 * 60 * 1000); // 5 minutes
  }

  /**
   * Execute a service operation with monitoring and error handling
   */
  async executeService<T>(
    serviceName: string,
    methodName: string,
    operation: () => Promise<ServiceResult<T>>,
    options: {
      userId?: string;
      cacheKey?: string;
      cacheTtl?: number;
      skipCache?: boolean;
    } = {}
  ): Promise<T> {
    const operationId = `${serviceName}.${methodName}`;
    const startTime = new Date();
    
    // Check cache first
    if (options.cacheKey && !options.skipCache && this.config.enableCaching) {
      const cached = this.getCachedResult(options.cacheKey);
      if (cached) {
        this.recordOperation(operationId, {
          serviceName,
          methodName,
          userId: options.userId,
          startTime,
          endTime: new Date(),
          duration: 0,
          success: true,
          cached: true,
        });
        return cached;
      }
    }

    let result: ServiceResult<T>;
    let error: any = null;
    
    try {
      // Execute with timeout
      if (this.config.timeout) {
        result = await Promise.race([
          operation(),
          new Promise<ServiceResult<T>>((_, reject) => 
            setTimeout(() => reject(new Error('Operation timeout')), this.config.timeout)
          ),
        ]);
      } else {
        result = await operation();
      }
    } catch (err) {
      error = err;
      result = {
        success: false,
        error: err instanceof Error ? err.message : 'Unknown error',
      };
    }

    const endTime = new Date();
    const duration = endTime.getTime() - startTime.getTime();

    // Record operation for monitoring
    this.recordOperation(operationId, {
      serviceName,
      methodName,
      userId: options.userId,
      startTime,
      endTime,
      duration,
      success: result.success,
      error: result.success ? undefined : result.error,
      cached: false,
    });

    // Cache successful results
    if (result.success && options.cacheKey && this.config.enableCaching) {
      this.cacheResult(options.cacheKey, result.data, options.cacheTtl || 5 * 60 * 1000);
    }

    // Handle service result
    if (result.success) {
      return result.data;
    } else {
      throw this.convertToTRPCError(result.error || 'Operation failed', error);
    }
  }

  /**
   * Create a service-integrated tRPC procedure handler
   */
  createServiceHandler<TInput, TOutput>(
    serviceName: string,
    methodName: string,
    serviceOperation: (input: TInput, userId: string, prisma: PrismaClient) => Promise<ServiceResult<TOutput>>,
    options: {
      cacheKeyGenerator?: (input: TInput, userId: string) => string;
      cacheTtl?: number;
      skipCache?: boolean;
    } = {}
  ) {
    return async (params: { input: TInput; ctx: { userId: string; prisma: PrismaClient } }) => {
      const { input, ctx } = params;
      const cacheKey = options.cacheKeyGenerator ? options.cacheKeyGenerator(input, ctx.userId) : undefined;
      
      return this.executeService(
        serviceName,
        methodName,
        () => serviceOperation(input, ctx.userId, ctx.prisma),
        {
          userId: ctx.userId,
          cacheKey,
          cacheTtl: options.cacheTtl,
          skipCache: options.skipCache,
        }
      );
    };
  }

  /**
   * Get performance statistics for service operations
   */
  getPerformanceStats(): {
    totalOperations: number;
    averageResponseTime: number;
    successRate: number;
    slowestOperations: Array<{ operation: string; averageTime: number; count: number }>;
    errorRate: number;
  } {
    const allOperations = Array.from(this.operations.values()).flat();
    
    if (allOperations.length === 0) {
      return {
        totalOperations: 0,
        averageResponseTime: 0,
        successRate: 0,
        slowestOperations: [],
        errorRate: 0,
      };
    }

    const totalOperations = allOperations.length;
    const successfulOperations = allOperations.filter(op => op.success);
    const averageResponseTime = allOperations.reduce((sum, op) => sum + (op.duration || 0), 0) / totalOperations;
    const successRate = (successfulOperations.length / totalOperations) * 100;
    const errorRate = ((totalOperations - successfulOperations.length) / totalOperations) * 100;

    // Calculate slowest operations
    const operationStats = new Map<string, { totalTime: number; count: number }>();
    
    for (const op of allOperations) {
      const key = `${op.serviceName}.${op.methodName}`;
      if (!operationStats.has(key)) {
        operationStats.set(key, { totalTime: 0, count: 0 });
      }
      const stats = operationStats.get(key)!;
      stats.totalTime += op.duration || 0;
      stats.count += 1;
    }

    const slowestOperations = Array.from(operationStats.entries())
      .map(([operation, stats]) => ({
        operation,
        averageTime: stats.totalTime / stats.count,
        count: stats.count,
      }))
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 10);

    return {
      totalOperations,
      averageResponseTime,
      successRate,
      slowestOperations,
      errorRate,
    };
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalEntries: number;
    hitRate: number;
    memoryUsage: number;
  } {
    const totalEntries = this.cache.size;
    const allOperations = Array.from(this.operations.values()).flat();
    const cachedOperations = allOperations.filter(op => op.cached);
    const hitRate = allOperations.length > 0 ? (cachedOperations.length / allOperations.length) * 100 : 0;
    
    // Estimate memory usage (rough approximation)
    const memoryUsage = Array.from(this.cache.values()).reduce((sum, entry) => {
      return sum + JSON.stringify(entry.data).length;
    }, 0);

    return {
      totalEntries,
      hitRate,
      memoryUsage,
    };
  }

  /**
   * Clear all caches and statistics
   */
  clearAll(): void {
    this.cache.clear();
    this.operations.clear();
  }

  /**
   * Record an operation for monitoring
   */
  private recordOperation(operationId: string, operation: ServiceOperation): void {
    if (!this.config.enablePerformanceMonitoring) return;
    
    if (!this.operations.has(operationId)) {
      this.operations.set(operationId, []);
    }
    
    const operations = this.operations.get(operationId)!;
    operations.push(operation);
    
    // Keep only recent operations to prevent memory leaks
    if (operations.length > 1000) {
      operations.shift();
    }
  }

  /**
   * Get cached result if available and not expired
   */
  private getCachedResult(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    const now = new Date();
    const age = now.getTime() - cached.timestamp.getTime();
    
    if (age > cached.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  /**
   * Cache a result with TTL
   */
  private cacheResult(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: new Date(),
      ttl,
    });
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = new Date();
    const expired = [];
    
    for (const [key, entry] of this.cache.entries()) {
      const age = now.getTime() - entry.timestamp.getTime();
      if (age > entry.ttl) {
        expired.push(key);
      }
    }
    
    for (const key of expired) {
      this.cache.delete(key);
    }
    
    if (expired.length > 0) {
      console.log(`Cleaned up ${expired.length} expired cache entries`);
    }
  }

  /**
   * Convert service error to appropriate tRPC error
   */
  private convertToTRPCError(message: string, originalError?: any): TRPCError {
    let code: any = 'INTERNAL_SERVER_ERROR';
    
    if (message.includes('Invalid') || message.includes('validation')) {
      code = 'BAD_REQUEST';
    } else if (message.includes('not found')) {
      code = 'NOT_FOUND';
    } else if (message.includes('unauthorized') || message.includes('access denied')) {
      code = 'UNAUTHORIZED';
    } else if (message.includes('forbidden') || message.includes('limit exceeded')) {
      code = 'FORBIDDEN';
    } else if (message.includes('conflict') || message.includes('already exists')) {
      code = 'CONFLICT';
    } else if (message.includes('unavailable') || message.includes('timeout')) {
      code = 'SERVICE_UNAVAILABLE';
    } else if (message.includes('required')) {
      code = 'BAD_REQUEST';
    }
    
    return new TRPCError({
      code,
      message,
      cause: originalError,
    });
  }
}

// Default service integration instance
let defaultIntegration: ServiceIntegration | null = null;

/**
 * Get the default service integration instance
 */
export function getServiceIntegration(config?: Partial<ServiceIntegrationConfig>): ServiceIntegration {
  if (!defaultIntegration) {
    defaultIntegration = new ServiceIntegration(config);
  }
  return defaultIntegration;
}

/**
 * Reset the default service integration instance
 */
export function resetServiceIntegration(): void {
  if (defaultIntegration) {
    defaultIntegration.clearAll();
    defaultIntegration = null;
  }
}

/**
 * Helper function to create service-integrated tRPC handlers
 */
export function createServiceHandler<TInput, TOutput>(
  serviceName: string,
  methodName: string,
  serviceOperation: (input: TInput, userId: string, prisma: PrismaClient) => Promise<ServiceResult<TOutput>>,
  options: {
    cacheKeyGenerator?: (input: TInput, userId: string) => string;
    cacheTtl?: number;
    skipCache?: boolean;
  } = {}
) {
  const integration = getServiceIntegration();
  return integration.createServiceHandler(serviceName, methodName, serviceOperation, options);
}

/**
 * Helper function to create cache key generators
 */
export const CacheKeyGenerators = {
  /**
   * Generate cache key for user-specific data
   */
  userSpecific: (prefix: string) => (input: any, userId: string) => `${prefix}:${userId}`,
  
  /**
   * Generate cache key for input-based data
   */
  inputBased: (prefix: string) => (input: any, userId: string) => `${prefix}:${JSON.stringify(input)}`,
  
  /**
   * Generate cache key for user and input combination
   */
  userAndInput: (prefix: string) => (input: any, userId: string) => `${prefix}:${userId}:${JSON.stringify(input)}`,
  
  /**
   * Generate cache key for global data
   */
  global: (prefix: string) => (input: any, userId: string) => `${prefix}:global`,
  
  /**
   * Generate cache key for time-based data
   */
  timeBased: (prefix: string, intervalMs: number) => (input: any, userId: string) => {
    const interval = Math.floor(Date.now() / intervalMs);
    return `${prefix}:${interval}:${JSON.stringify(input)}`;
  },
};

/**
 * Cache TTL constants
 */
export const CacheTTL = {
  ONE_MINUTE: 60 * 1000,
  FIVE_MINUTES: 5 * 60 * 1000,
  TEN_MINUTES: 10 * 60 * 1000,
  THIRTY_MINUTES: 30 * 60 * 1000,
  ONE_HOUR: 60 * 60 * 1000,
  ONE_DAY: 24 * 60 * 60 * 1000,
};
