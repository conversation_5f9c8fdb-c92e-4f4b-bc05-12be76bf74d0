/**
 * Prisma Configuration Factory
 *
 * Centralized configuration for all Prisma client instances.
 * Ensures consistent logging, middleware, and connection settings
 * across web app, scripts, and tests.
 *
 * Implements connection pooling best practices for optimal performance.
 */

import { PrismaClient, type Prisma } from "../../prisma/generated";
import { performanceMonitor } from "./performance-monitor";

export interface PrismaConfigOptions {
  /** Custom database URL (defaults to process.env.DATABASE_URL) */
  databaseUrl?: string;
  /** Enable query logging regardless of environment */
  forceQueryLogs?: boolean;
  /** Disable performance middleware (useful for tests) */
  disableMiddleware?: boolean;
  /** Custom log levels */
  logLevels?: Prisma.LogLevel[];
  /** Instance identifier for logging */
  instanceId?: string;
  /** Connection pool settings */
  connectionPool?: {
    /** Maximum number of connections in the pool */
    poolSize?: number;
    /** Connection timeout in milliseconds */
    connectionTimeout?: number;
    /** Idle timeout in milliseconds */
    idleTimeout?: number;
  };
}

/**
 * Parse connection string to extract and modify parameters
 */
function parseConnectionString(url: string): {
  parsed: URL;
  params: Record<string, string>;
} {
  try {
    const parsed = new URL(url);
    const params: Record<string, string> = {};

    // Extract existing parameters
    parsed.searchParams.forEach((value, key) => {
      params[key] = value;
    });

    return { parsed, params };
  } catch (error) {
    console.error("❌ Invalid database URL:", error);
    throw new Error("Invalid database URL format");
  }
}

/**
 * Optimize connection string for pooling
 */
function optimizeConnectionString(
  url: string,
  options: {
    poolSize?: number;
    connectionTimeout?: number;
    idleTimeout?: number;
  } = {}
): string {
  if (!url) return url;

  const {
    poolSize = 10,
    connectionTimeout = 30000,
    idleTimeout = 60000,
  } = options;

  try {
    const { parsed, params } = parseConnectionString(url);

    // Check if this is a pooled connection (pgbouncer)
    const isPooled = parsed.port === "6543" || params.pgbouncer === "true";

    // Set optimal connection parameters
    if (isPooled) {
      // For pgbouncer connections
      params.pgbouncer = "true";
      params.connection_limit = poolSize.toString();
      params.pool_timeout = connectionTimeout.toString();
    } else {
      // For direct connections
      params.connection_limit = Math.min(poolSize, 5).toString(); // Lower for direct
      params.connect_timeout = Math.floor(connectionTimeout / 1000).toString();
      params.idle_timeout = Math.floor(idleTimeout / 1000).toString();
    }

    // Rebuild URL with optimized parameters
    const newUrl = new URL(parsed.toString());
    newUrl.search = ""; // Clear existing params

    // Add all parameters
    Object.entries(params).forEach(([key, value]) => {
      newUrl.searchParams.append(key, value);
    });

    return newUrl.toString();
  } catch (error) {
    console.warn("⚠️ Failed to optimize connection string:", error);
    return url;
  }
}

/**
 * Create standardized Prisma client configuration
 */
export function createPrismaConfig(options: PrismaConfigOptions = {}): Prisma.PrismaClientOptions {
  const {
    databaseUrl = process.env.DATABASE_URL,
    forceQueryLogs = false,
    logLevels,
    instanceId = "default",
    connectionPool,
  } = options;

  // Determine log levels based on environment and options
  const getLogLevels = (): Prisma.LogLevel[] => {
    if (logLevels) return logLevels;

    if (forceQueryLogs || process.env.ENABLE_PRISMA_QUERY_LOGS === "true") {
      return ["query", "error", "warn", "info"];
    }

    if (process.env.NODE_ENV === "development") {
      return ["warn", "error"];
    }

    return ["error"];
  };

  // Optimize connection string if provided
  const optimizedUrl = databaseUrl
    ? optimizeConnectionString(databaseUrl, connectionPool)
    : undefined;

  // Safe logging that doesn't expose credentials
  if (process.env.NODE_ENV === "development" || process.env.VERBOSE_LOGGING === "true") {
    console.log(`🔍 Prisma Config [${instanceId}]: Initializing client`);
    console.log(`🔍 Prisma Config [${instanceId}]: DATABASE_URL exists:`, !!optimizedUrl);
    console.log(`🔍 Prisma Config [${instanceId}]: Log levels:`, getLogLevels());

    if (connectionPool) {
      console.log(`🔍 Prisma Config [${instanceId}]: Connection pool configured:`, {
        poolSize: connectionPool.poolSize || 10,
        connectionTimeout: connectionPool.connectionTimeout || 30000,
        idleTimeout: connectionPool.idleTimeout || 60000,
      });
    }
  }

  return {
    log: getLogLevels(),
    errorFormat: "pretty",
    datasources: {
      db: {
        url: optimizedUrl,
      },
    },
  };
}

/**
 * Create a Prisma client with standard configuration and middleware
 */
export function createPrismaClient(options: PrismaConfigOptions = {}): PrismaClient {
  const { disableMiddleware = false, instanceId = "default" } = options;
  const config = createPrismaConfig(options);

  console.log(`🔧 Creating Prisma Client [${instanceId}]...`);
  console.log(`🔧 Middleware enabled: ${!disableMiddleware}`);
  console.log(`🔧 Performance monitoring: ${!disableMiddleware ? "enabled" : "disabled"}`);

  const client = new PrismaClient(config);

  // Add performance monitoring middleware using modern Prisma extensions
  if (!disableMiddleware) {
    const extendedClient = client.$extends({
      query: {
        $allModels: {
          async $allOperations({ model, operation, args, query }) {
            const startTime = performance.now();
            const operationName = `${model}.${operation}`;

            try {
              // Use trackQuery to properly handle performance monitoring
              const result = await performanceMonitor.trackQuery(
                operationName,
                () => query(args),
                args,
                undefined // userId not available in middleware context
              );

              return result;
            } catch (error) {
              const duration = performance.now() - startTime;
              console.error(
                `❌ Failed Query [${instanceId}]: ${operationName} failed after ${duration.toFixed(2)}ms`,
                {
                  error: error instanceof Error ? error.message : String(error),
                  model,
                  operation,
                }
              );
              throw error;
            }
          },
        },
      },
    });

    console.log(`✅ Prisma Client [${instanceId}] created with performance monitoring`);
    return extendedClient as PrismaClient;
  }

  console.log(`✅ Prisma Client [${instanceId}] created without middleware`);
  return client;
}

/**
 * Connection health check utility
 */
export async function checkPrismaConnection(
  client: PrismaClient, 
  instanceId: string = "default"
): Promise<boolean> {
  try {
    await client.$queryRaw`SELECT 1 as health_check`;
    console.log(`✅ Prisma Health Check [${instanceId}]: Connection healthy`);
    return true;
  } catch (error) {
    console.error(`❌ Prisma Health Check [${instanceId}]: Connection failed`, error);
    return false;
  }
}

/**
 * Graceful disconnect with error handling
 */
export async function disconnectPrisma(
  client: PrismaClient, 
  instanceId: string = "default"
): Promise<void> {
  try {
    await client.$disconnect();
    console.log(`🔌 Prisma [${instanceId}]: Disconnected successfully`);
  } catch (error) {
    console.error(`❌ Prisma [${instanceId}]: Disconnect failed`, error);
  }
}
