/**
 * Mention Sync Service Unit Tests
 * 
 * Tests for Twitter mention synchronization functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { MentionSyncService } from '../../../apps/web/src/lib/mention-sync-service'
import { mockTwitterClient, mockPrismaClient, mockEnvironmentVariables, clearAllMocks } from '../../utils/mocks'

// Mock dependencies
vi.mock('../../../apps/web/src/lib/twitter-client')
vi.mock('../../../apps/web/src/lib/benji')

describe('Mention Sync Service', () => {
  let syncService: MentionSyncService
  let mockPrisma: any
  
  beforeEach(() => {
    clearAllMocks()
    mockPrisma = mockPrismaClient()
    mockTwitterClient()
    mockEnvironmentVariables()
    
    syncService = new MentionSyncService(mockPrisma)
    
    // Mock Benji agent
    vi.mocked(require('./benji').getBenjiForUser).mockResolvedValue({
      performFullAnalysis: vi.fn().mockResolvedValue({
        bullishScore: 75,
        importanceScore: 80,
        keywords: ['crypto', 'bitcoin'],
        analysisData: {
          sentiment: 'positive',
          priority: 'high'
        }
      })
    })
    
    console.log('🧪 Mention Sync Service Test: Setup complete')
  })
  
  describe('getUserSyncLimits', () => {
    it('should return user sync limits based on plan', async () => {
      // Arrange
      const userId = 'test-user-123'
      const mockUser = {
        id: userId,
        plan: {
          name: 'reply-guy',
          features: [
            { feature: 'MENTIONS_PER_SYNC', limit: 50 },
            { feature: 'MAX_TOTAL_MENTIONS', limit: 1000 }
          ]
        }
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      
      // Act
      const result = await syncService.getUserSyncLimits(userId)
      
      // Assert
      expect(result).toMatchObject({
        mentionsPerSync: 50,
        maxTotalMentions: 1000,
        planName: 'reply-guy'
      })
      
      console.log('✅ Mention Sync Service Test: User limits retrieval works')
    })
    
    it('should return default limits for user without specific features', async () => {
      // Arrange
      const userId = 'test-user-123'
      const mockUser = {
        id: userId,
        plan: {
          name: 'basic',
          features: []
        }
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      
      // Act
      const result = await syncService.getUserSyncLimits(userId)
      
      // Assert
      expect(result).toMatchObject({
        mentionsPerSync: 20, // Default
        maxTotalMentions: 500, // Default
        planName: 'basic'
      })
      
      console.log('✅ Mention Sync Service Test: Default limits work')
    })
  })
  
  describe('syncAccountMentions', () => {
    it('should sync mentions for a monitored account', async () => {
      // Arrange
      const accountId = 'test-account-123'
      const userId = 'test-user-123'
      
      const mockAccount = {
        id: accountId,
        userId: userId,
        twitterHandle: 'testhandle',
        twitterUserId: 'twitter-123',
        isActive: true,
        lastCheckedAt: null
      }
      
      const mockUser = {
        id: userId,
        plan: {
          features: [
            { feature: 'MENTIONS_PER_SYNC', limit: 50 },
            { feature: 'MAX_TOTAL_MENTIONS', limit: 1000 }
          ]
        }
      }
      
      const { mockTweet } = mockTwitterClient()
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      mockPrisma.monitoredAccount.findFirst.mockResolvedValue(mockAccount)
      mockPrisma.mention.findUnique.mockResolvedValue(null) // No existing mention
      mockPrisma.mention.count.mockResolvedValue(100) // Current total
      mockPrisma.mention.create.mockResolvedValue({
        id: mockTweet.id,
        content: mockTweet.text
      })
      mockPrisma.monitoredAccount.update.mockResolvedValue(mockAccount)
      
      // Act
      const result = await syncService.syncAccountMentions(accountId, userId)
      
      // Assert
      expect(result.success).toBe(true)
      expect(result.accountHandle).toBe('testhandle')
      expect(result.newMentions).toBe(1)
      expect(result.totalMentions).toBe(101)
      
      expect(mockPrisma.mention.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          id: mockTweet.id,
          userId: userId,
          content: mockTweet.text,
          authorHandle: mockTweet.author.userName,
          processed: false,
          isUserTweet: true
        })
      })
      
      console.log('✅ Mention Sync Service Test: Account sync works')
    })
    
    it('should skip duplicate mentions', async () => {
      // Arrange
      const accountId = 'test-account-123'
      const userId = 'test-user-123'
      
      const mockAccount = {
        id: accountId,
        userId: userId,
        twitterHandle: 'testhandle',
        isActive: true
      }
      
      const mockUser = {
        id: userId,
        plan: { features: [] }
      }
      
      const { mockTweet } = mockTwitterClient()
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      mockPrisma.monitoredAccount.findFirst.mockResolvedValue(mockAccount)
      mockPrisma.mention.findUnique.mockResolvedValue({ id: mockTweet.id }) // Existing mention
      mockPrisma.mention.count.mockResolvedValue(100)
      
      // Act
      const result = await syncService.syncAccountMentions(accountId, userId)
      
      // Assert
      expect(result.success).toBe(true)
      expect(result.newMentions).toBe(0) // No new mentions due to duplicate
      expect(mockPrisma.mention.create).not.toHaveBeenCalled()
      
      console.log('✅ Mention Sync Service Test: Duplicate detection works')
    })
    
    it('should handle Twitter API errors', async () => {
      // Arrange
      const accountId = 'test-account-123'
      const userId = 'test-user-123'
      
      const mockAccount = {
        id: accountId,
        userId: userId,
        twitterHandle: 'testhandle',
        isActive: true
      }
      
      const mockUser = {
        id: userId,
        plan: { features: [] }
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      mockPrisma.monitoredAccount.findFirst.mockResolvedValue(mockAccount)
      
      // Mock Twitter API error
      vi.mocked(require('./twitter-client').twitterClient.getUserMentions)
        .mockRejectedValue(new Error('Twitter API rate limit exceeded'))
      
      // Act
      const result = await syncService.syncAccountMentions(accountId, userId)
      
      // Assert
      expect(result.success).toBe(false)
      expect(result.error).toContain('Twitter API rate limit exceeded')
      
      console.log('✅ Mention Sync Service Test: Twitter API error handling works')
    })
    
    it('should enforce mention limits', async () => {
      // Arrange
      const accountId = 'test-account-123'
      const userId = 'test-user-123'
      
      const mockAccount = {
        id: accountId,
        userId: userId,
        twitterHandle: 'testhandle',
        isActive: true
      }
      
      const mockUser = {
        id: userId,
        plan: {
          features: [
            { feature: 'MAX_TOTAL_MENTIONS', limit: 100 }
          ]
        }
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      mockPrisma.monitoredAccount.findFirst.mockResolvedValue(mockAccount)
      mockPrisma.mention.count.mockResolvedValue(100) // At limit
      
      // Act
      const result = await syncService.syncAccountMentions(accountId, userId)
      
      // Assert
      expect(result.success).toBe(false)
      expect(result.limitReached).toBe(true)
      expect(result.maxAllowed).toBe(100)
      expect(result.currentCount).toBe(100)
      
      console.log('✅ Mention Sync Service Test: Mention limit enforcement works')
    })
  })
  
  describe('syncAllUserAccounts', () => {
    it('should sync all active accounts for a user', async () => {
      // Arrange
      const userId = 'test-user-123'
      
      const mockAccounts = [
        {
          id: 'account-1',
          userId: userId,
          twitterHandle: 'handle1',
          isActive: true
        },
        {
          id: 'account-2',
          userId: userId,
          twitterHandle: 'handle2',
          isActive: true
        }
      ]
      
      mockPrisma.monitoredAccount.findMany.mockResolvedValue(mockAccounts)
      
      // Mock successful sync for both accounts
      const syncSpy = vi.spyOn(syncService, 'syncAccountMentions')
      syncSpy.mockResolvedValueOnce({
        success: true,
        accountHandle: 'handle1',
        newMentions: 2,
        totalMentions: 102
      })
      syncSpy.mockResolvedValueOnce({
        success: true,
        accountHandle: 'handle2',
        newMentions: 1,
        totalMentions: 51
      })
      
      // Act
      const result = await syncService.syncAllUserAccounts(userId)
      
      // Assert
      expect(result.success).toBe(true)
      expect(result.totalNewMentions).toBe(3)
      expect(result.results).toHaveLength(2)
      expect(result.errors).toHaveLength(0)
      
      expect(syncSpy).toHaveBeenCalledTimes(2)
      expect(syncSpy).toHaveBeenCalledWith('account-1', userId)
      expect(syncSpy).toHaveBeenCalledWith('account-2', userId)
      
      console.log('✅ Mention Sync Service Test: Bulk account sync works')
    })
    
    it('should handle partial failures in bulk sync', async () => {
      // Arrange
      const userId = 'test-user-123'
      
      const mockAccounts = [
        {
          id: 'account-1',
          userId: userId,
          twitterHandle: 'handle1',
          isActive: true
        },
        {
          id: 'account-2',
          userId: userId,
          twitterHandle: 'handle2',
          isActive: true
        }
      ]
      
      mockPrisma.monitoredAccount.findMany.mockResolvedValue(mockAccounts)
      
      // Mock one success, one failure
      const syncSpy = vi.spyOn(syncService, 'syncAccountMentions')
      syncSpy.mockResolvedValueOnce({
        success: true,
        accountHandle: 'handle1',
        newMentions: 2,
        totalMentions: 102
      })
      syncSpy.mockResolvedValueOnce({
        success: false,
        accountHandle: 'handle2',
        newMentions: 0,
        totalMentions: 50,
        error: 'API error'
      })
      
      // Act
      const result = await syncService.syncAllUserAccounts(userId)
      
      // Assert
      expect(result.success).toBe(true) // Overall success even with partial failures
      expect(result.totalNewMentions).toBe(2)
      expect(result.results).toHaveLength(2)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0]).toContain('@handle2: API error')
      
      console.log('✅ Mention Sync Service Test: Partial failure handling works')
    })
    
    it('should return empty result for user with no accounts', async () => {
      // Arrange
      const userId = 'test-user-no-accounts'
      
      mockPrisma.monitoredAccount.findMany.mockResolvedValue([])
      
      // Act
      const result = await syncService.syncAllUserAccounts(userId)
      
      // Assert
      expect(result.success).toBe(true)
      expect(result.totalNewMentions).toBe(0)
      expect(result.results).toHaveLength(0)
      expect(result.errors).toHaveLength(0)
      
      console.log('✅ Mention Sync Service Test: No accounts handling works')
    })
  })
  
  describe('analyzeMentionWithAI', () => {
    it('should analyze mention with AI and update database', async () => {
      // Arrange
      const userId = 'test-user-123'
      const mockTweet = {
        id: '*********',
        text: 'Bitcoin is going to the moon! 🚀',
        author: {
          name: 'Crypto Fan',
          userName: 'cryptofan',
          followers: 5000,
          isBlueVerified: true,
          profilePicture: 'https://example.com/avatar.jpg'
        }
      }
      
      mockPrisma.mention.update.mockResolvedValue({
        id: mockTweet.id,
        bullishScore: 75,
        importanceScore: 80
      })
      
      // Act
      await syncService.analyzeMentionWithAI(mockTweet as any, userId)
      
      // Assert
      expect(vi.mocked(require('./benji-agent').getBenjiForUser)).toHaveBeenCalledWith(userId)
      expect(mockPrisma.mention.update).toHaveBeenCalledWith({
        where: { id: mockTweet.id },
        data: {
          bullishScore: 75,
          importanceScore: 80,
          keywords: ['crypto', 'bitcoin'],
          analysisData: {
            sentiment: 'positive',
            priority: 'high'
          },
          processed: true
        }
      })
      
      console.log('✅ Mention Sync Service Test: AI analysis works')
    })
    
    it('should handle AI analysis errors gracefully', async () => {
      // Arrange
      const userId = 'test-user-123'
      const mockTweet = {
        id: '*********',
        text: 'Test tweet',
        author: {
          name: 'Test User',
          userName: 'testuser'
        }
      }
      
      // Mock AI analysis error
      vi.mocked(require('./benji-agent').getBenjiForUser).mockRejectedValue(
        new Error('AI analysis failed')
      )
      
      // Act & Assert
      await expect(syncService.analyzeMentionWithAI(mockTweet as any, userId))
        .rejects.toThrow('AI analysis failed')
      
      console.log('✅ Mention Sync Service Test: AI analysis error handling works')
    })
  })
})
